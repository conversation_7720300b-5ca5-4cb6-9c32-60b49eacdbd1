#!/usr/bin/env node

/**
 * Rename Package Aliases Script
 * Changes @novaest/ui, @novaest/admin, etc. to @ui, @admin, etc.
 */

import { execSync } from 'node:child_process'
import fs from 'node:fs'
import path from 'node:path'

const packageMappings = {
  '@novaest/shared': '@shared',
  '@novaest/ui': '@ui',
  '@novaest/owner': '@owner',
  '@novaest/contractor': '@contractor',
  '@novaest/admin': '@admin'
}

const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m'
}

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`)
}

function updateFileContent(filePath, content) {
  let updatedContent = content
  let hasChanges = false

  Object.entries(packageMappings).forEach(([oldName, newName]) => {
    // Replace import statements
    const importRegex = new RegExp(
      `from ['"]${oldName.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}['"]`,
      'g'
    )
    if (importRegex.test(updatedContent)) {
      updatedContent = updatedContent.replace(importRegex, `from '${newName}'`)
      hasChanges = true
    }

    // Replace import statements with path
    const importPathRegex = new RegExp(
      `from ['"]${oldName.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}/([^'"]+)['"]`,
      'g'
    )
    if (importPathRegex.test(updatedContent)) {
      updatedContent = updatedContent.replace(
        importPathRegex,
        `from '${newName}/$1'`
      )
      hasChanges = true
    }

    // Replace dynamic imports
    const dynamicImportRegex = new RegExp(
      `import\\(['"]${oldName.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}['"]\\)`,
      'g'
    )
    if (dynamicImportRegex.test(updatedContent)) {
      updatedContent = updatedContent.replace(
        dynamicImportRegex,
        `import('${newName}')`
      )
      hasChanges = true
    }

    // Replace require statements
    const requireRegex = new RegExp(
      `require\\(['"]${oldName.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}['"]\\)`,
      'g'
    )
    if (requireRegex.test(updatedContent)) {
      updatedContent = updatedContent.replace(
        requireRegex,
        `require('${newName}')`
      )
      hasChanges = true
    }

    // Replace in JSON strings (for package.json, tsconfig.json)
    const jsonStringRegex = new RegExp(
      `"${oldName.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}"`,
      'g'
    )
    if (jsonStringRegex.test(updatedContent)) {
      updatedContent = updatedContent.replace(jsonStringRegex, `"${newName}"`)
      hasChanges = true
    }

    // Replace in JSON object keys
    const jsonKeyRegex = new RegExp(
      `"${oldName.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}":`,
      'g'
    )
    if (jsonKeyRegex.test(updatedContent)) {
      updatedContent = updatedContent.replace(jsonKeyRegex, `"${newName}":`)
      hasChanges = true
    }

    // Replace in JSON object keys with path
    const jsonKeyPathRegex = new RegExp(
      `"${oldName.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}/([^"]+)":`,
      'g'
    )
    if (jsonKeyPathRegex.test(updatedContent)) {
      updatedContent = updatedContent.replace(
        jsonKeyPathRegex,
        `"${newName}/$1":`
      )
      hasChanges = true
    }
  })

  if (hasChanges) {
    fs.writeFileSync(filePath, updatedContent)
    return true
  }

  return false
}

function processFile(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8')
    const updated = updateFileContent(filePath, content)

    if (updated) {
      log(`✅ Updated: ${filePath}`, 'green')
      return true
    }

    return false
  } catch (error) {
    log(`❌ Error processing ${filePath}: ${error.message}`, 'red')
    return false
  }
}

function findFilesToUpdate() {
  const filesToCheck = []

  // TypeScript/JavaScript files
  const codeExtensions = ['.ts', '.tsx', '.js', '.jsx']

  // Config files
  const configFiles = [
    'apps/web/vite.config.ts',
    'apps/web/tsconfig.json',
    'apps/web/package.json',
    'package.json',
    'pnpm-lock.yaml'
  ]

  // Add config files
  configFiles.forEach((file) => {
    if (fs.existsSync(file)) {
      filesToCheck.push(file)
    }
  })

  // Recursively find code files
  function scanDirectory(dir) {
    if (!fs.existsSync(dir)) return

    const items = fs.readdirSync(dir)

    for (const item of items) {
      const itemPath = path.join(dir, item)
      const stat = fs.statSync(itemPath)

      if (stat.isDirectory()) {
        // Skip node_modules, dist, .git
        if (!['node_modules', 'dist', '.git', '.turbo'].includes(item)) {
          scanDirectory(itemPath)
        }
      } else {
        const ext = path.extname(item)
        if (codeExtensions.includes(ext) || item.endsWith('.md')) {
          filesToCheck.push(itemPath)
        }
      }
    }
  }

  // Scan source directories
  scanDirectory('apps/web/src')
  scanDirectory('packages')
  scanDirectory('docs')

  // Add root markdown files
  if (fs.existsSync('README.md')) filesToCheck.push('README.md')

  return filesToCheck
}

function updatePackageNames() {
  log('📦 Updating package names in package.json files...', 'blue')

  const packageDirs = [
    'packages/shared',
    'packages/ui',
    'packages/owner',
    'packages/contractor',
    'packages/admin'
  ]

  packageDirs.forEach((dir) => {
    const packageJsonPath = path.join(dir, 'package.json')
    if (fs.existsSync(packageJsonPath)) {
      const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'))
      const oldName = packageJson.name
      const newName = packageMappings[oldName]

      if (newName) {
        packageJson.name = newName
        fs.writeFileSync(
          packageJsonPath,
          `${JSON.stringify(packageJson, null, 2)}\n`
        )
        log(`✅ Renamed package: ${oldName} → ${newName}`, 'green')
      }
    }
  })
}

function main() {
  log('🔄 Renaming Package Aliases', 'bright')
  log('Changing @novaest/* to @* aliases\n', 'cyan')

  // Step 1: Update package names
  updatePackageNames()

  // Step 2: Find all files to update
  log('\n🔍 Finding files to update...', 'blue')
  const filesToUpdate = findFilesToUpdate()
  log(`Found ${filesToUpdate.length} files to check`, 'cyan')

  // Step 3: Update file contents
  log('\n📝 Updating file contents...', 'blue')
  let updatedCount = 0

  filesToUpdate.forEach((file) => {
    if (processFile(file)) {
      updatedCount++
    }
  })

  log(`\n✅ Updated ${updatedCount} files`, 'green')

  // Step 4: Update pnpm lockfile
  log('\n🔄 Updating pnpm lockfile...', 'blue')
  try {
    execSync('pnpm install', { stdio: 'pipe' })
    log('✅ pnpm lockfile updated', 'green')
  } catch (_error) {
    log('⚠️  Please run `pnpm install` manually to update lockfile', 'yellow')
  }

  log('\n🎉 Package alias renaming complete!', 'bright')
  log('\nNew aliases:', 'cyan')
  Object.entries(packageMappings).forEach(([old, newAlias]) => {
    log(`  ${old} → ${newAlias}`, 'green')
  })

  log('\n📋 Next steps:', 'yellow')
  log('1. Run `pnpm dev` to test development')
  log('2. Run `pnpm build` to test production build')
  log('3. Check for any remaining references manually')
}

// Run the script
main()
