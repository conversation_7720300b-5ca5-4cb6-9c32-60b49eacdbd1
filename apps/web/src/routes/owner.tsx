import { OwnerDashboardRoute } from '@owner'
import { createFileRoute, redirect } from '@tanstack/react-router'
import { PackageErrorBoundary } from '@/components/ErrorBoundary'
import type { RouterContext } from '@/main'

export const Route = createFileRoute('/owner')({
  beforeLoad: ({ context }: { context: RouterContext }) => {
    // Check if user is authenticated and has owner role
    if (!context.auth?.user) {
      throw redirect({
        to: '/login',
        search: {
          redirect: '/owner'
        }
      })
    }

    if (context.auth.user.role !== 'owner') {
      throw redirect({
        to: '/',
        search: {
          error: 'Access denied. Owner role required.'
        }
      })
    }
  },
  component: () => (
    <PackageErrorBoundary packageName="@owner">
      <OwnerDashboardRoute />
    </PackageErrorBoundary>
  )
})
