import { AdminDashboardRoute } from '@admin'
import { createFileRoute, redirect } from '@tanstack/react-router'
import { PackageErrorBoundary } from '@/components/ErrorBoundary'
import type { RouterContext } from '@/main'

export const Route = createFileRoute('/admin')({
  beforeLoad: ({ context }: { context: RouterContext }) => {
    // Check if user is authenticated and has admin role
    if (!context.auth?.user) {
      throw redirect({
        to: '/login',
        search: {
          redirect: '/admin'
        }
      })
    }

    if (context.auth.user.role !== 'admin') {
      throw redirect({
        to: '/',
        search: {
          error: 'Access denied. Administrator role required.'
        }
      })
    }
  },
  component: () => (
    <PackageErrorBoundary packageName="@admin">
      <AdminDashboardRoute />
    </PackageErrorBoundary>
  )
})
