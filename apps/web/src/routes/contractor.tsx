import { ContractorDashboardRoute } from '@contractor'
import { createFileRoute, redirect } from '@tanstack/react-router'
import { PackageErrorBoundary } from '@/components/ErrorBoundary'
import type { RouterContext } from '@/main'

export const Route = createFileRoute('/contractor')({
  beforeLoad: ({ context }: { context: RouterContext }) => {
    // Check if user is authenticated and has contractor role
    if (!context.auth?.user) {
      throw redirect({
        to: '/login',
        search: {
          redirect: '/contractor'
        }
      })
    }

    if (context.auth.user.role !== 'contractor') {
      throw redirect({
        to: '/',
        search: {
          error: 'Access denied. Contractor role required.'
        }
      })
    }
  },
  component: () => (
    <PackageErrorBoundary packageName="@contractor">
      <ContractorDashboardRoute />
    </PackageErrorBoundary>
  )
})
