// Core Mantine exports
export * from '@mantine/carousel'
export * from '@mantine/core'
export * from '@mantine/dates'
export * from '@mantine/dropzone'
export * from '@mantine/hooks'
export * from '@mantine/modals'
export * from '@mantine/notifications'

// Re-export icons
export * from '@tabler/icons-react'
export { Avatar } from './components/atoms/Avatar'
export { Badge } from './components/atoms/Badge'
// Export Atomic Design components with specific names to avoid conflicts
export { Button } from './components/atoms/Button'
export { Icon } from './components/atoms/Icon'
export { Input } from './components/atoms/Input'
// Export custom components with specific names to avoid conflicts
export { Loader as PageLoader } from './components/Loader'
export { NotificationItem } from './components/molecules/NotificationItem'
export { ProjectCard } from './components/molecules/ProjectCard'
// Export molecules
export { SearchBar } from './components/molecules/SearchBar'
export { StatCard } from './components/molecules/StatCard'
export { UserCard } from './components/molecules/UserCard'
// Export organisms
export * from './components/organisms'
// Export templates
export * from './components/templates'

// Export theme provider with custom name
export { MantineProvider } from './theme/MantineProvider'
