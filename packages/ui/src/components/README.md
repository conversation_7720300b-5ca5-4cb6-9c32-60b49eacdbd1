# Atomic Design System - @ui Package

Hệ thống component được tổ chức theo mô hình **Atomic Design** để đảm bảo t<PERSON>h nhất quán, tái sử dụng và dễ bảo trì.

## 🧬 Cấu trúc Atomic Design

### 1. **Atoms** - Component cơ bản nhất
Các component không thể chia nhỏ hơn, là building blocks cơ bản nhất:

- **Button** - Các loại button với variants khác nhau
- **Input** - Text input, password input, số, email...
- **Avatar** - Hiển thị ảnh đại diện user
- **Badge** - Nhãn trạng thái, số lượng
- **Icon** - Wrapper cho Tabler icons với sizing nhất quán

```typescript
// Ví dụ sử dụng atoms
import { Button, Input, Avatar, Badge } from '@ui/atoms'

<Button variant="primary" size="md">Save</Button>
<Input placeholder="Enter email" type="email" />
<Avatar src="/user.jpg" size="lg" />
<Badge color="green">Active</Badge>
```

### 2. **Molecules** - Kết hợp atoms
Các component được tạo từ việc kết hợp nhiều atoms:

- **SearchBar** - Input + Button + Icon
- **UserCard** - Avatar + Text + Badge
- **ProjectCard** - Card container với title, description, status
- **NotificationItem** - Icon + Text + Timestamp + Actions
- **StatCard** - Icon + Title + Value + Trend

```typescript
// Ví dụ sử dụng molecules
import { SearchBar, UserCard, ProjectCard } from '@ui/molecules'

<SearchBar onSearch={handleSearch} placeholder="Search projects..." />
<UserCard user={userData} showStatus />
<ProjectCard project={projectData} onEdit={handleEdit} />
```

### 3. **Organisms** - Kết hợp molecules
Các section lớn của interface, kết hợp nhiều molecules:

- **ProjectList** - Danh sách ProjectCard với pagination
- **UserProfile** - Form profile với nhiều sections
- **NavigationHeader** - Header với menu, search, user info
- **DashboardStats** - Grid của StatCard

```typescript
// Ví dụ sử dụng organisms
import { ProjectList, UserProfile, DashboardStats } from '@ui/organisms'

<DashboardStats stats={dashboardData} />
<ProjectList projects={projects} onProjectClick={handleClick} />
<UserProfile user={currentUser} onUpdate={handleUpdate} />
```

### 4. **Templates** - Layout structures
Cấu trúc layout tổng thể cho các trang:

- **DashboardLayout** - Layout cho dashboard với sidebar, header
- **FormLayout** - Layout cho forms với validation display

```typescript
// Ví dụ sử dụng templates
import { DashboardLayout, FormLayout } from '@ui/templates'

<DashboardLayout>
  <DashboardStats stats={stats} />
  <ProjectList projects={projects} />
</DashboardLayout>
```

## 🎯 Nguyên tắc thiết kế

### 1. **Single Responsibility**
Mỗi component chỉ có một trách nhiệm duy nhất và rõ ràng.

### 2. **Composition over Inheritance**
Ưu tiên kết hợp components thay vì kế thừa.

### 3. **Props Interface**
Tất cả components đều có TypeScript interface rõ ràng cho props.

### 4. **Consistent Naming**
- Atoms: Tên đơn giản (Button, Input)
- Molecules: Tên mô tả chức năng (SearchBar, UserCard)
- Organisms: Tên mô tả section (ProjectList, DashboardStats)
- Templates: Tên mô tả layout (DashboardLayout)

### 5. **Mantine Integration**
Tất cả components đều sử dụng Mantine core components làm base, đảm bảo:
- Consistent theming
- Accessibility
- Responsive design
- Dark mode support

## 📁 Cấu trúc thư mục

```
src/components/
├── atoms/
│   ├── Button/
│   │   ├── index.ts
│   │   ├── Button.tsx
│   │   └── Button.types.ts
│   ├── Input/
│   ├── Avatar/
│   ├── Badge/
│   └── Icon/
├── molecules/
│   ├── SearchBar/
│   ├── UserCard/
│   ├── ProjectCard/
│   ├── NotificationItem/
│   └── StatCard/
├── organisms/
│   ├── ProjectList/
│   ├── UserProfile/
│   ├── NavigationHeader/
│   └── DashboardStats/
├── templates/
│   ├── DashboardLayout/
│   └── FormLayout/
└── index.ts
```

## 🔄 Migration từ cấu trúc cũ

Các component hiện tại sẽ được refactor theo pattern mới:
- `Loader` → `atoms/Loader`
- Dashboard components → Tách thành molecules và organisms
- Layout logic → `templates/`

## 🚀 Sử dụng trong role packages

```typescript
// @owner package
import { DashboardLayout, ProjectList, StatCard } from '@ui'

// @contractor package
import { DashboardLayout, TaskList, TimeTracking } from '@ui'

// @admin package
import { DashboardLayout, UserManagement, SystemStats } from '@ui'
```

## 📖 Usage Examples

### Using Atoms
```tsx
import { Button, Input, Avatar, Badge } from '@ui'

function MyComponent() {
  return (
    <div>
      <Button variant="primary" size="md">Click me</Button>
      <Input placeholder="Enter text..." />
      <Avatar name="John Doe" size="md" showStatus isOnline />
      <Badge color="success">Active</Badge>
    </div>
  )
}
```

### Using Molecules
```tsx
import { SearchBar, StatCard, UserCard, ProjectCard } from '@ui'

function Dashboard() {
  return (
    <div>
      <SearchBar onSearch={handleSearch} placeholder="Search projects..." />

      <StatCard
        title="Total Projects"
        value={42}
        icon={IconBriefcase}
        trend={{ value: 12, isPositive: true }}
      />

      <UserCard
        user={{ name: "John Doe", email: "<EMAIL>" }}
        showContact
        showRole
      />
    </div>
  )
}
```

### Using Organisms
```tsx
import { DashboardStats, ProjectList, NavigationHeader } from '@ui'

function ProjectsPage() {
  return (
    <div>
      <NavigationHeader
        title="Novaest"
        user={currentUser}
        showSearch
        showNotifications
      />

      <DashboardStats
        stats={statsData}
        columns={{ base: 1, md: 2, lg: 4 }}
        showTrends
      />

      <ProjectList
        projects={projects}
        showSearch
        showProgress
        onProjectClick={handleProjectClick}
      />
    </div>
  )
}
```

### Using Templates
```tsx
import { DashboardLayout, FormLayout } from '@ui'

function MyDashboard() {
  return (
    <DashboardLayout
      title="Novaest"
      user={currentUser}
      sidebarNavigation={navItems}
      showSearch
      showNotifications
    >
      <YourPageContent />
    </DashboardLayout>
  )
}

function CreateProject() {
  return (
    <FormLayout
      title="Create New Project"
      sections={formSections}
      actions={<Button type="submit">Create</Button>}
      withCard
      centered
    >
      <YourFormContent />
    </FormLayout>
  )
}
```

## 🔄 Migration Guide

When migrating existing components to use the Atomic Design system:

1. **Replace basic UI elements** with atoms (Button, Input, Avatar, etc.)
2. **Combine related functionality** into molecules (SearchBar, UserCard, etc.)
3. **Group molecules** into organisms for page sections
4. **Use templates** for consistent page layouts

### Example Migration

**Before:**
```tsx
// In OwnerDashboard.tsx
<Paper p="md" withBorder>
  <Group justify="space-between">
    <Text fw={500}>Total Projects</Text>
    <IconBriefcase size={20} />
  </Group>
  <Text size="xl" fw={700}>42</Text>
  <Text size="sm" c="green">+12% vs last month</Text>
</Paper>
```

**After:**
```tsx
// Using StatCard molecule
<StatCard
  title="Total Projects"
  value={42}
  icon={IconBriefcase}
  trend={{ value: 12, isPositive: true, label: 'vs last month' }}
/>
```
