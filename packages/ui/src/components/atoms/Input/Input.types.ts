import type { TextInputProps } from '@mantine/core'

export interface InputProps extends Omit<TextInputProps, 'size'> {
  /**
   * Input size
   */
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl'

  /**
   * Input type
   */
  type?: 'text' | 'email' | 'password' | 'number' | 'tel' | 'url' | 'search'

  /**
   * Label for the input
   */
  label?: string

  /**
   * Placeholder text
   */
  placeholder?: string

  /**
   * Error message
   */
  error?: string | boolean

  /**
   * Helper text
   */
  description?: string

  /**
   * Required field indicator
   */
  required?: boolean

  /**
   * Disabled state
   */
  disabled?: boolean

  /**
   * Icon to display on the left
   */
  leftIcon?: React.ReactNode

  /**
   * Icon to display on the right
   */
  rightIcon?: React.ReactNode
}
