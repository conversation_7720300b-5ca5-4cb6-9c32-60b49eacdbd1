import { TextInput } from '@mantine/core'
import type { InputProps } from './Input.types'

/**
 * Custom Input component extending Mantine TextInput with consistent styling
 *
 * @example
 * ```tsx
 * <Input
 *   label="Email Address"
 *   type="email"
 *   placeholder="Enter your email"
 *   required
 * />
 *
 * <Input
 *   label="Search"
 *   leftIcon={<IconSearch />}
 *   placeholder="Search projects..."
 * />
 * ```
 */
export function Input({
  size = 'md',
  type = 'text',
  label,
  placeholder,
  error,
  description,
  required = false,
  disabled = false,
  leftIcon,
  rightIcon,
  ...props
}: InputProps) {
  return (
    <TextInput
      size={size}
      type={type}
      label={label}
      placeholder={placeholder}
      error={error}
      description={description}
      required={required}
      disabled={disabled}
      leftSection={leftIcon}
      rightSection={rightIcon}
      {...props}
    />
  )
}
