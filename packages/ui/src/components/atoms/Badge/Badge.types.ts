import type { BadgeProps as MantineBadgeProps } from '@mantine/core'

export interface BadgeProps
  extends Omit<MantineBadgeProps, 'variant' | 'color'> {
  /**
   * Badge variant
   */
  variant?: 'filled' | 'light' | 'outline' | 'dot'

  /**
   * Badge color - supports both Mantine colors and custom status colors
   */
  color?:
    | 'blue'
    | 'green'
    | 'red'
    | 'yellow'
    | 'orange'
    | 'purple'
    | 'gray'
    | 'success'
    | 'warning'
    | 'error'
    | 'info'
    | 'primary'
    | 'secondary'

  /**
   * Badge size
   */
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl'

  /**
   * Badge content
   */
  children: React.ReactNode
}
