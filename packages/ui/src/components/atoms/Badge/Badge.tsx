import { Badge as MantineBadge } from '@mantine/core'
import type { BadgeProps } from './Badge.types'

/**
 * Custom Badge component with semantic color mapping
 *
 * @example
 * ```tsx
 * <Badge color="success" variant="light">Active</Badge>
 * <Badge color="warning" variant="filled">Pending</Badge>
 * <Badge color="error" variant="outline">Cancelled</Badge>
 * ```
 */
export function Badge({
  variant = 'light',
  color = 'primary',
  size = 'sm',
  children,
  ...props
}: BadgeProps) {
  // Map semantic colors to Mantine colors
  const getMantineColor = (semanticColor: string) => {
    switch (semanticColor) {
      case 'success':
        return 'green'
      case 'warning':
        return 'yellow'
      case 'error':
        return 'red'
      case 'info':
        return 'blue'
      case 'primary':
        return 'blue'
      case 'secondary':
        return 'gray'
      default:
        return semanticColor
    }
  }

  return (
    <MantineBadge
      variant={variant}
      color={getMantineColor(color)}
      size={size}
      {...props}
    >
      {children}
    </MantineBadge>
  )
}
