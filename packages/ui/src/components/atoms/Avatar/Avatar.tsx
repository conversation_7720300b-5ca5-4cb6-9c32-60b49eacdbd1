import { Indicator, Avatar as MantineAvatar } from '@mantine/core'
import type { AvatarProps } from './Avatar.types'

/**
 * Custom Avatar component with status indicator support
 *
 * @example
 * ```tsx
 * <Avatar
 *   src="/user.jpg"
 *   alt="John <PERSON>"
 *   size="lg"
 *   showStatus
 *   isOnline
 * />
 *
 * <Avatar
 *   name="John Do<PERSON>"
 *   color="blue"
 *   size="md"
 * />
 * ```
 */
export function Avatar({
  size = 'md',
  src,
  alt,
  name,
  color,
  variant = 'filled',
  showStatus = false,
  isOnline = false,
  ...props
}: AvatarProps) {
  // Generate initials from name
  const getInitials = (fullName?: string) => {
    if (!fullName) return ''
    return fullName
      .split(' ')
      .map((word) => word.charAt(0))
      .join('')
      .toUpperCase()
      .slice(0, 2)
  }

  const avatar = (
    <MantineAvatar
      src={src}
      alt={alt || name}
      size={size}
      color={color}
      variant={variant}
      {...props}
    >
      {!src && getInitials(name)}
    </MantineAvatar>
  )

  if (showStatus) {
    return (
      <Indicator
        color={isOnline ? 'green' : 'gray'}
        size={typeof size === 'number' ? Math.max(8, size * 0.2) : 12}
        position="bottom-end"
        offset={2}
        withBorder
      >
        {avatar}
      </Indicator>
    )
  }

  return avatar
}
