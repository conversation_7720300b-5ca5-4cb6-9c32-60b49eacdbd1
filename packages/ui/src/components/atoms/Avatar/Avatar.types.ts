import type { AvatarProps as MantineAvatarProps } from '@mantine/core'

export interface AvatarProps extends Omit<MantineAvatarProps, 'size'> {
  /**
   * Avatar size
   */
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl' | number

  /**
   * Image source URL
   */
  src?: string

  /**
   * Alt text for the image
   */
  alt?: string

  /**
   * Fallback text (usually initials)
   */
  name?: string

  /**
   * Avatar color when no image is provided
   */
  color?: string

  /**
   * Avatar variant
   */
  variant?: 'filled' | 'light' | 'outline' | 'transparent'

  /**
   * Show online status indicator
   */
  showStatus?: boolean

  /**
   * Online status
   */
  isOnline?: boolean
}
