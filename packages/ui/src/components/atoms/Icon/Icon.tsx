import type { IconProps } from './Icon.types'

/**
 * Consistent Icon wrapper for Tabler icons with standardized sizing
 *
 * @example
 * ```tsx
 * import { IconUser, IconSettings } from '@tabler/icons-react'
 *
 * <Icon icon={IconUser} size="md" />
 * <Icon icon={IconSettings} size={24} color="blue" />
 * ```
 */
export function Icon({
  icon: IconComponent,
  size = 'md',
  color,
  stroke = 1.5,
  className,
  style,
  onClick,
  ...props
}: IconProps) {
  // Convert size presets to pixel values
  const getPixelSize = (sizeValue: string | number) => {
    if (typeof sizeValue === 'number') return sizeValue

    switch (sizeValue) {
      case 'xs':
        return 12
      case 'sm':
        return 16
      case 'md':
        return 20
      case 'lg':
        return 24
      case 'xl':
        return 28
      default:
        return 20
    }
  }

  return (
    <IconComponent
      size={getPixelSize(size)}
      color={color}
      stroke={stroke}
      className={className}
      onClick={onClick}
      style={{
        cursor: onClick ? 'pointer' : undefined,
        ...style
      }}
      {...props}
    />
  )
}
