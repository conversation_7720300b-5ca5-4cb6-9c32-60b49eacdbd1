import type { Icon as TablerIcon } from '@tabler/icons-react'

export interface IconProps {
  /**
   * Tabler icon component
   */
  icon: TablerIcon

  /**
   * Icon size - can be a preset or custom number
   */
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl' | number

  /**
   * Icon color
   */
  color?: string

  /**
   * Stroke width
   */
  stroke?: number

  /**
   * Additional CSS class
   */
  className?: string

  /**
   * Inline styles
   */
  style?: React.CSSProperties

  /**
   * Click handler
   */
  onClick?: () => void
}
