import type { ButtonProps as MantineButtonProps } from '@mantine/core'

export interface ButtonProps extends Omit<MantineButtonProps, 'variant'> {
  /**
   * Button variant - extends Mantine variants with custom ones
   */
  variant?:
    | 'primary'
    | 'secondary'
    | 'danger'
    | 'success'
    | 'outline'
    | 'subtle'
    | 'light'
    | 'filled'

  /**
   * Button size
   */
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl'

  /**
   * Loading state
   */
  loading?: boolean

  /**
   * Disabled state
   */
  disabled?: boolean

  /**
   * Full width button
   */
  fullWidth?: boolean

  /**
   * Icon to display on the left
   */
  leftIcon?: React.ReactNode

  /**
   * Icon to display on the right
   */
  rightIcon?: React.ReactNode

  /**
   * Ref to the button element (React 19 style)
   */
  ref?: React.Ref<HTMLButtonElement>
}
