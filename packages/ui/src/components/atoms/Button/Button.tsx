import {
  createPolymorphicComponent,
  Button as MantineButton
} from '@mantine/core'
import type { ButtonProps } from './Button.types'

/**
 * Custom Button component extending Mantine Button with project-specific variants
 *
 * @example
 * ```tsx
 * <Button variant="primary" size="md" onClick={handleClick}>
 *   Save Changes
 * </Button>
 *
 * <Button variant="danger" loading={isDeleting} leftIcon={<IconTrash />}>
 *   Delete Project
 * </Button>
 * ```
 */
function _Button({
  variant = 'primary',
  size = 'md',
  loading = false,
  disabled = false,
  fullWidth = false,
  leftIcon,
  rightIcon,
  children,
  ref,
  ...props
}: ButtonProps) {
  // Map custom variants to Mantine variants
  const getMantineVariant = (customVariant: string) => {
    switch (customVariant) {
      case 'primary':
        return 'filled'
      case 'secondary':
        return 'outline'
      case 'danger':
        return 'filled'
      case 'success':
        return 'filled'
      default:
        return customVariant
    }
  }

  // Map custom variants to colors
  const getColor = (customVariant: string) => {
    switch (customVariant) {
      case 'primary':
        return 'blue'
      case 'secondary':
        return 'gray'
      case 'danger':
        return 'red'
      case 'success':
        return 'green'
      default:
        return undefined
    }
  }

  return (
    <MantineButton
      ref={ref}
      variant={getMantineVariant(variant)}
      color={getColor(variant)}
      size={size}
      loading={loading}
      disabled={disabled}
      fullWidth={fullWidth}
      leftSection={leftIcon}
      rightSection={rightIcon}
      {...props}
    >
      {children}
    </MantineButton>
  )
}

_Button.displayName = 'Button'

export const Button = createPolymorphicComponent<'button', ButtonProps>(_Button)
