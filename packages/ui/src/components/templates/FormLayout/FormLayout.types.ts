export interface FormSection {
  id: string
  title: string
  description?: string
  children: React.ReactNode
}

export interface FormLayoutProps {
  /**
   * Form content - can be sections or direct form elements
   */
  children?: React.ReactNode

  /**
   * Form sections for organized layout
   */
  sections?: FormSection[]

  /**
   * Form title
   */
  title?: string

  /**
   * Form description
   */
  description?: string

  /**
   * Show form progress (for multi-step forms)
   */
  showProgress?: boolean

  /**
   * Current step (0-based)
   */
  currentStep?: number

  /**
   * Total steps
   */
  totalSteps?: number

  /**
   * Step labels
   */
  stepLabels?: string[]

  /**
   * Form actions (buttons, etc.)
   */
  actions?: React.ReactNode

  /**
   * Show actions at top
   */
  actionsPosition?: 'top' | 'bottom' | 'both'

  /**
   * Form loading state
   */
  loading?: boolean

  /**
   * Form error message
   */
  error?: string

  /**
   * Form success message
   */
  success?: string

  /**
   * Maximum width of the form
   */
  maxWidth?: number | string

  /**
   * Center the form
   */
  centered?: boolean

  /**
   * Show form in a card
   */
  withCard?: boolean
}
