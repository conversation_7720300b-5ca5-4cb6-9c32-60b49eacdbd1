import {
  Alert,
  Box,
  Container,
  Divider,
  Group,
  LoadingOverlay,
  Paper,
  Stack,
  Stepper,
  Text,
  Title
} from '@mantine/core'
import { IconAlertCircle, IconCheck } from '@tabler/icons-react'
import { Icon } from '../../atoms/Icon'
import type { FormLayoutProps } from './FormLayout.types'

/**
 * FormLayout template for consistent form presentation
 *
 * @example
 * ```tsx
 * <FormLayout
 *   title="Create New Project"
 *   description="Fill in the details to create a new project"
 *   sections={[
 *     {
 *       id: 'basic',
 *       title: 'Basic Information',
 *       description: 'Project name and description',
 *       children: <BasicInfoForm />
 *     },
 *     {
 *       id: 'details',
 *       title: 'Project Details',
 *       children: <ProjectDetailsForm />
 *     }
 *   ]}
 *   actions={
 *     <Group>
 *       <Button variant="outline">Cancel</Button>
 *       <Button type="submit">Create Project</Button>
 *     </Group>
 *   }
 *   withCard
 *   centered
 * />
 * ```
 */
export function FormLayout({
  children,
  sections = [],
  title,
  description,
  showProgress = false,
  currentStep = 0,
  totalSteps = 1,
  stepLabels = [],
  actions,
  actionsPosition = 'bottom',
  loading = false,
  error,
  success,
  maxWidth = 600,
  centered = false,
  withCard = false,
  ...props
}: FormLayoutProps) {
  const content = (
    <Box style={{ maxWidth, width: '100%' }} pos="relative">
      <LoadingOverlay visible={loading} />

      <Stack gap="xl">
        {/* Header */}
        {(title || description) && (
          <Stack gap="sm">
            {title && (
              <Title order={2} size="h3">
                {title}
              </Title>
            )}
            {description && (
              <Text c="dimmed" size="sm">
                {description}
              </Text>
            )}
          </Stack>
        )}

        {/* Progress Stepper */}
        {showProgress && totalSteps > 1 && (
          <Stepper active={currentStep} size="sm" allowNextStepsSelect={false}>
            {Array.from({ length: totalSteps }, (_, index) => (
              <Stepper.Step
                key={index}
                label={stepLabels[index] || `Step ${index + 1}`}
              />
            ))}
          </Stepper>
        )}

        {/* Messages */}
        {error && (
          <Alert
            color="red"
            icon={<Icon icon={IconAlertCircle} size="sm" />}
            title="Error"
          >
            {error}
          </Alert>
        )}

        {success && (
          <Alert
            color="green"
            icon={<Icon icon={IconCheck} size="sm" />}
            title="Success"
          >
            {success}
          </Alert>
        )}

        {/* Top Actions */}
        {actions &&
          (actionsPosition === 'top' || actionsPosition === 'both') && (
            <>
              <Group justify="flex-end">{actions}</Group>
              <Divider />
            </>
          )}

        {/* Form Content */}
        {sections.length > 0 ? (
          <Stack gap="xl">
            {sections.map((section, index) => (
              <Box key={section.id}>
                {/* Section Header */}
                <Stack gap="sm" mb="md">
                  <Title order={4} size="h5">
                    {section.title}
                  </Title>
                  {section.description && (
                    <Text size="sm" c="dimmed">
                      {section.description}
                    </Text>
                  )}
                </Stack>

                {/* Section Content */}
                {section.children}

                {/* Section Divider */}
                {index < sections.length - 1 && <Divider mt="xl" />}
              </Box>
            ))}
          </Stack>
        ) : (
          children
        )}

        {/* Bottom Actions */}
        {actions &&
          (actionsPosition === 'bottom' || actionsPosition === 'both') && (
            <>
              <Divider />
              <Group justify="flex-end">{actions}</Group>
            </>
          )}
      </Stack>
    </Box>
  )

  const wrappedContent = withCard ? (
    <Paper p="xl" withBorder>
      {content}
    </Paper>
  ) : (
    content
  )

  return centered ? (
    <Container size="sm" py="xl" {...props}>
      <Stack align="center">{wrappedContent}</Stack>
    </Container>
  ) : (
    <Box {...props}>{wrappedContent}</Box>
  )
}
