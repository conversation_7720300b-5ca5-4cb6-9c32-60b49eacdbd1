import type { TablerIcon } from '@tabler/icons-react'
import type { User } from '../../molecules/UserCard'
import type { NavigationItem } from '../../organisms/NavigationHeader'

export interface SidebarItem {
  id: string
  label: string
  icon?: TablerIcon
  href?: string
  onClick?: () => void
  active?: boolean
  disabled?: boolean
  badge?: string | number
  children?: SidebarItem[]
}

export interface DashboardLayoutProps {
  /**
   * Layout content
   */
  children: React.ReactNode

  /**
   * Application title
   */
  title?: string

  /**
   * Logo source
   */
  logoSrc?: string

  /**
   * Current user
   */
  user?: User

  /**
   * Header navigation items
   */
  headerNavigation?: NavigationItem[]

  /**
   * Sidebar navigation items
   */
  sidebarNavigation?: SidebarItem[]

  /**
   * Show sidebar
   */
  showSidebar?: boolean

  /**
   * Show header search
   */
  showSearch?: boolean

  /**
   * Show notifications
   */
  showNotifications?: boolean

  /**
   * Notification count
   */
  notificationCount?: number

  /**
   * Sidebar collapsed state
   */
  sidebarCollapsed?: boolean

  /**
   * Mobile sidebar opened state
   */
  mobileSidebarOpened?: boolean

  /**
   * Handlers
   */
  onSearch?: (query: string) => void
  onNotificationClick?: () => void
  onLogout?: () => void
  onSidebarToggle?: () => void
  onMobileSidebarToggle?: () => void

  /**
   * Additional header actions
   */
  headerActions?: React.ReactNode
}
