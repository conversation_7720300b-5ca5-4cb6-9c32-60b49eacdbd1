import {
  AppShell,
  Box,
  Collapse,
  Group,
  ScrollArea,
  Stack,
  Text,
  UnstyledButton
} from '@mantine/core'
import { IconChevronRight } from '@tabler/icons-react'
import { useState } from 'react'
import { Badge } from '../../atoms/Badge'
import { Icon } from '../../atoms/Icon'
import { NavigationHeader } from '../../organisms/NavigationHeader'
import type { DashboardLayoutProps, SidebarItem } from './DashboardLayout.types'

/**
 * DashboardLayout template providing complete dashboard structure
 *
 * @example
 * ```tsx
 * <DashboardLayout
 *   title="Novaest"
 *   user={currentUser}
 *   showSearch
 *   showNotifications
 *   sidebarNavigation={[
 *     { id: '1', label: 'Dashboard', icon: IconDashboard, active: true },
 *     { id: '2', label: 'Projects', icon: IconBriefcase },
 *     { id: '3', label: 'Users', icon: IconUsers }
 *   ]}
 *   onLogout={handleLogout}
 * >
 *   <YourPageContent />
 * </DashboardLayout>
 * ```
 */
export function DashboardLayout({
  children,
  title,
  logoSrc,
  user,
  headerNavigation = [],
  sidebarNavigation = [],
  showSidebar = true,
  showSearch = false,
  showNotifications = false,
  notificationCount = 0,
  sidebarCollapsed = false,
  mobileSidebarOpened = false,
  onSearch,
  onNotificationClick,
  onLogout,
  onSidebarToggle,
  onMobileSidebarToggle,
  headerActions,
  ...props
}: DashboardLayoutProps) {
  const [expandedItems, setExpandedItems] = useState<Set<string>>(new Set())

  const toggleExpanded = (itemId: string) => {
    const newExpanded = new Set(expandedItems)
    if (newExpanded.has(itemId)) {
      newExpanded.delete(itemId)
    } else {
      newExpanded.add(itemId)
    }
    setExpandedItems(newExpanded)
  }

  const renderSidebarItem = (item: SidebarItem, level = 0) => {
    const hasChildren = item.children && item.children.length > 0
    const isExpanded = expandedItems.has(item.id)
    const paddingLeft = 16 + level * 16

    return (
      <Box key={item.id}>
        <UnstyledButton
          w="100%"
          p="sm"
          style={{
            paddingLeft,
            borderRadius: 8,
            backgroundColor: item.active
              ? 'var(--mantine-color-blue-light)'
              : undefined,
            color: item.active ? 'var(--mantine-color-blue-filled)' : undefined,
            opacity: item.disabled ? 0.5 : 1,
            cursor: item.disabled ? 'not-allowed' : 'pointer'
          }}
          onClick={() => {
            if (item.disabled) return

            if (hasChildren) {
              toggleExpanded(item.id)
            } else {
              item.onClick?.()
            }
          }}
          disabled={item.disabled}
        >
          <Group justify="space-between" gap="sm">
            <Group gap="sm" style={{ flex: 1 }}>
              {item.icon && (
                <Icon
                  icon={item.icon}
                  size="sm"
                  color={item.active ? 'blue' : 'gray'}
                />
              )}

              {!sidebarCollapsed && (
                <Text
                  size="sm"
                  fw={item.active ? 600 : 400}
                  style={{ flex: 1 }}
                >
                  {item.label}
                </Text>
              )}
            </Group>

            {!sidebarCollapsed && (
              <Group gap="xs">
                {item.badge && (
                  <Badge color="primary" size="xs">
                    {item.badge}
                  </Badge>
                )}

                {hasChildren && (
                  <Icon
                    icon={IconChevronRight}
                    size="xs"
                    style={{
                      transform: isExpanded ? 'rotate(90deg)' : 'rotate(0deg)',
                      transition: 'transform 0.2s'
                    }}
                  />
                )}
              </Group>
            )}
          </Group>
        </UnstyledButton>

        {/* Render children */}
        {hasChildren && !sidebarCollapsed && (
          <Collapse in={isExpanded}>
            <Stack gap={0} mt="xs">
              {item.children!.map((child) =>
                renderSidebarItem(child, level + 1)
              )}
            </Stack>
          </Collapse>
        )}
      </Box>
    )
  }

  return (
    <AppShell
      header={{ height: 60 }}
      navbar={
        showSidebar
          ? {
              width: sidebarCollapsed ? 80 : 280,
              breakpoint: 'md',
              collapsed: { mobile: !mobileSidebarOpened }
            }
          : undefined
      }
      padding="md"
      {...props}
    >
      {/* Header */}
      <AppShell.Header>
        <NavigationHeader
          title={title}
          logoSrc={logoSrc}
          navigationItems={headerNavigation}
          user={user}
          showSearch={showSearch}
          showNotifications={showNotifications}
          notificationCount={notificationCount}
          onSearch={onSearch}
          onNotificationClick={onNotificationClick}
          onLogout={onLogout}
          onMobileMenuToggle={onMobileSidebarToggle}
          actions={headerActions}
        />
      </AppShell.Header>

      {/* Sidebar */}
      {showSidebar && (
        <AppShell.Navbar p="md">
          <ScrollArea style={{ flex: 1 }}>
            <Stack gap="xs">
              {sidebarNavigation.map((item) => renderSidebarItem(item))}
            </Stack>
          </ScrollArea>
        </AppShell.Navbar>
      )}

      {/* Main Content */}
      <AppShell.Main>{children}</AppShell.Main>
    </AppShell>
  )
}
