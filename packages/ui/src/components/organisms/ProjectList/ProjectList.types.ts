import type { Project } from '../../molecules/ProjectCard'

export interface ProjectListProps {
  /**
   * Array of projects to display
   */
  projects: Project[]

  /**
   * Loading state
   */
  loading?: boolean

  /**
   * Empty state message
   */
  emptyMessage?: string

  /**
   * Show search bar
   */
  showSearch?: boolean

  /**
   * Search placeholder text
   */
  searchPlaceholder?: string

  /**
   * Show project progress
   */
  showProgress?: boolean

  /**
   * Show project budget
   */
  showBudget?: boolean

  /**
   * Show project deadline
   */
  showDeadline?: boolean

  /**
   * Show project location
   */
  showLocation?: boolean

  /**
   * Show contractor count
   */
  showContractorCount?: boolean

  /**
   * Show project tags
   */
  showTags?: boolean

  /**
   * Grid columns configuration
   */
  columns?: {
    base?: number
    sm?: number
    md?: number
    lg?: number
    xl?: number
  }

  /**
   * Project click handler
   */
  onProjectClick?: (project: Project) => void

  /**
   * Search handler
   */
  onSearch?: (query: string) => void

  /**
   * Render custom actions for each project card
   */
  renderActions?: (project: Project) => React.ReactNode
}
