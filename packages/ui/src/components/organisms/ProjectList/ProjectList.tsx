import { Center, SimpleGrid, Stack, Text } from '@mantine/core'
import { useState } from 'react'
import { ProjectCard } from '../../molecules/ProjectCard'
import { SearchBar } from '../../molecules/SearchBar'
import type { ProjectListProps } from './ProjectList.types'

/**
 * ProjectList organism for displaying a searchable grid of project cards
 *
 * @example
 * ```tsx
 * <ProjectList
 *   projects={projects}
 *   showSearch
 *   showProgress
 *   showBudget
 *   columns={{ base: 1, md: 2, lg: 3 }}
 *   onProjectClick={handleProjectClick}
 *   onSearch={handleSearch}
 *   renderActions={(project) => (
 *     <Menu>
 *       <Menu.Item onClick={() => editProject(project)}>Edit</Menu.Item>
 *       <Menu.Item onClick={() => deleteProject(project)}>Delete</Menu.Item>
 *     </Menu>
 *   )}
 * />
 * ```
 */
export function ProjectList({
  projects,
  loading = false,
  emptyMessage = 'No projects found',
  showSearch = false,
  searchPlaceholder = 'Search projects...',
  showProgress = false,
  showBudget = false,
  showDeadline = false,
  showLocation = false,
  showContractorCount = false,
  showTags = false,
  columns = { base: 1, md: 2, lg: 3 },
  onProjectClick,
  onSearch,
  renderActions,
  ...props
}: ProjectListProps) {
  const [searchQuery, setSearchQuery] = useState('')

  // Filter projects based on search query
  const filteredProjects = projects.filter((project) => {
    if (!searchQuery) return true

    const query = searchQuery.toLowerCase()
    return (
      project.title.toLowerCase().includes(query) ||
      project.description?.toLowerCase().includes(query) ||
      project.location?.toLowerCase().includes(query) ||
      project.tags?.some((tag) => tag.toLowerCase().includes(query))
    )
  })

  const handleSearch = (query: string) => {
    setSearchQuery(query)
    onSearch?.(query)
  }

  // Generate loading skeleton
  const loadingCards = Array.from({ length: 6 }, (_, index) => (
    <ProjectCard
      key={`loading-${index}`}
      project={{} as any}
      loading
      showProgress={showProgress}
      showBudget={showBudget}
      showDeadline={showDeadline}
      showLocation={showLocation}
      showContractorCount={showContractorCount}
      showTags={showTags}
    />
  ))

  return (
    <Stack gap="md" {...props}>
      {/* Search Bar */}
      {showSearch && (
        <SearchBar
          placeholder={searchPlaceholder}
          onSearch={handleSearch}
          value={searchQuery}
          onChange={setSearchQuery}
        />
      )}

      {/* Project Grid */}
      {loading ? (
        <SimpleGrid cols={columns}>{loadingCards}</SimpleGrid>
      ) : filteredProjects.length > 0 ? (
        <SimpleGrid cols={columns}>
          {filteredProjects.map((project) => (
            <ProjectCard
              key={project.id}
              project={project}
              showProgress={showProgress}
              showBudget={showBudget}
              showDeadline={showDeadline}
              showLocation={showLocation}
              showContractorCount={showContractorCount}
              showTags={showTags}
              onClick={onProjectClick}
              actions={renderActions?.(project)}
            />
          ))}
        </SimpleGrid>
      ) : (
        <Center py="xl">
          <Text c="dimmed" size="lg">
            {searchQuery
              ? `No projects found for "${searchQuery}"`
              : emptyMessage}
          </Text>
        </Center>
      )}
    </Stack>
  )
}
