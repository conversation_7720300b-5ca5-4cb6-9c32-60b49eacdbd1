import { SimpleGrid } from '@mantine/core'
import { StatCard } from '../../molecules/StatCard'
import type { DashboardStatsProps } from './DashboardStats.types'

/**
 * DashboardStats organism for displaying a grid of statistics cards
 *
 * @example
 * ```tsx
 * <DashboardStats
 *   stats={[
 *     {
 *       id: '1',
 *       title: 'Total Projects',
 *       value: 42,
 *       icon: IconBriefcase,
 *       iconColor: 'blue',
 *       trend: { value: 12, isPositive: true, label: 'vs last month' }
 *     },
 *     {
 *       id: '2',
 *       title: 'Active Users',
 *       value: 1234,
 *       icon: IconUsers,
 *       iconColor: 'green'
 *     }
 *   ]}
 *   columns={{ base: 1, sm: 2, lg: 4 }}
 *   showTrends
 * />
 * ```
 */
export function DashboardStats({
  stats,
  columns = { base: 1, sm: 2, lg: 4 },
  loading = false,
  showTrends = true,
  size = 'md',
  className,
  ...props
}: DashboardStatsProps) {
  return (
    <SimpleGrid cols={columns} className={className} {...props}>
      {stats.map((stat) => (
        <StatCard
          key={stat.id}
          title={stat.title}
          value={stat.value}
          icon={stat.icon}
          iconColor={stat.iconColor}
          description={stat.description}
          trend={showTrends ? stat.trend : undefined}
          loading={loading}
          onClick={stat.onClick}
        />
      ))}
    </SimpleGrid>
  )
}
