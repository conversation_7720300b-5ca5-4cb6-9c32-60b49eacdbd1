import type { Icon } from '@tabler/icons-react'

export interface StatItem {
  id: string
  title: string
  value: string | number
  icon?: Icon
  iconColor?: string
  description?: string
  trend?: {
    value: number
    label?: string
    isPositive?: boolean
  }
  onClick?: () => void
}

export interface DashboardStatsProps {
  /**
   * Array of stat items to display
   */
  stats: StatItem[]

  /**
   * Number of columns for the grid
   */
  columns?: {
    base?: number
    sm?: number
    md?: number
    lg?: number
    xl?: number
  }

  /**
   * Loading state
   */
  loading?: boolean

  /**
   * Show trend indicators
   */
  showTrends?: boolean

  /**
   * Card size
   */
  size?: 'sm' | 'md' | 'lg'

  /**
   * Additional CSS class
   */
  className?: string
}
