import {
  ActionIcon,
  Box,
  Burger,
  Group,
  Image,
  Indicator,
  Menu,
  Paper,
  Tabs,
  Text
} from '@mantine/core'
import {
  IconBell,
  IconLogout,
  IconSettings,
  IconUser
} from '@tabler/icons-react'
import { Avatar } from '../../atoms/Avatar'
import { Badge } from '../../atoms/Badge'
import { Icon } from '../../atoms/Icon'
import { SearchBar } from '../../molecules/SearchBar'
import type { NavigationHeaderProps } from './NavigationHeader.types'

/**
 * NavigationHeader organism for application header with navigation, search, and user menu
 *
 * @example
 * ```tsx
 * <NavigationHeader
 *   title="Novaest"
 *   navigationItems={[
 *     { id: '1', label: 'Dashboard', href: '/dashboard', active: true },
 *     { id: '2', label: 'Projects', href: '/projects' },
 *     { id: '3', label: 'Contractors', href: '/contractors', badge: 5 }
 *   ]}
 *   user={currentUser}
 *   showSearch
 *   showNotifications
 *   notificationCount={3}
 *   onSearch={handleSearch}
 *   onLogout={handleLogout}
 * />
 * ```
 */
export function NavigationHeader({
  title,
  logoSrc,
  navigationItems = [],
  user,
  showSearch = false,
  searchPlaceholder = 'Search...',
  showNotifications = false,
  notificationCount = 0,
  showUserMenu = true,
  onMobileMenuToggle,
  onSearch,
  onNotificationClick,
  onUserMenuClick,
  onLogout,
  actions,
  ...props
}: NavigationHeaderProps) {
  return (
    <Paper p="md" withBorder {...props}>
      <Group justify="space-between" h={60}>
        {/* Left Section - Logo & Navigation */}
        <Group gap="xl">
          {/* Mobile Menu Toggle */}
          {onMobileMenuToggle && (
            <Burger
              opened={false}
              onClick={onMobileMenuToggle}
              hiddenFrom="md"
              size="sm"
            />
          )}

          {/* Logo & Title */}
          <Group gap="sm">
            {logoSrc && <Image src={logoSrc} alt={title} h={32} w="auto" />}
            {title && (
              <Text fw={700} size="lg" c="blue">
                {title}
              </Text>
            )}
          </Group>

          {/* Navigation Items */}
          {navigationItems.length > 0 && (
            <Box visibleFrom="md">
              <Tabs
                variant="pills"
                value={navigationItems.find((item) => item.active)?.id}
              >
                <Tabs.List>
                  {navigationItems.map((item) => (
                    <Tabs.Tab
                      key={item.id}
                      value={item.id}
                      disabled={item.disabled}
                      onClick={item.onClick}
                      rightSection={
                        item.badge ? (
                          <Badge color="primary" size="xs">
                            {item.badge}
                          </Badge>
                        ) : undefined
                      }
                    >
                      {item.label}
                    </Tabs.Tab>
                  ))}
                </Tabs.List>
              </Tabs>
            </Box>
          )}
        </Group>

        {/* Right Section - Search, Notifications, User Menu */}
        <Group gap="md">
          {/* Search Bar */}
          {showSearch && onSearch && (
            <Box visibleFrom="sm" style={{ width: 300 }}>
              <SearchBar
                placeholder={searchPlaceholder}
                onSearch={onSearch}
                size="sm"
              />
            </Box>
          )}

          {/* Custom Actions */}
          {actions}

          {/* Notifications */}
          {showNotifications && (
            <Indicator
              color="red"
              size={16}
              disabled={notificationCount === 0}
              label={notificationCount > 99 ? '99+' : notificationCount}
            >
              <ActionIcon
                variant="subtle"
                size="lg"
                onClick={onNotificationClick}
              >
                <Icon icon={IconBell} size="md" />
              </ActionIcon>
            </Indicator>
          )}

          {/* User Menu */}
          {showUserMenu && user && (
            <Menu shadow="md" width={200}>
              <Menu.Target>
                <Group style={{ cursor: 'pointer' }} onClick={onUserMenuClick}>
                  <Avatar
                    src={user.avatar}
                    name={user.name}
                    size="sm"
                    showStatus
                    isOnline={user.status === 'online'}
                  />
                  <Box visibleFrom="sm">
                    <Text size="sm" fw={500}>
                      {user.name}
                    </Text>
                    {user.role && (
                      <Text size="xs" c="dimmed">
                        {user.role}
                      </Text>
                    )}
                  </Box>
                </Group>
              </Menu.Target>

              <Menu.Dropdown>
                <Menu.Label>Account</Menu.Label>
                <Menu.Item leftSection={<Icon icon={IconUser} size="sm" />}>
                  Profile
                </Menu.Item>
                <Menu.Item leftSection={<Icon icon={IconSettings} size="sm" />}>
                  Settings
                </Menu.Item>

                <Menu.Divider />

                <Menu.Item
                  color="red"
                  leftSection={<Icon icon={IconLogout} size="sm" />}
                  onClick={onLogout}
                >
                  Logout
                </Menu.Item>
              </Menu.Dropdown>
            </Menu>
          )}
        </Group>
      </Group>
    </Paper>
  )
}
