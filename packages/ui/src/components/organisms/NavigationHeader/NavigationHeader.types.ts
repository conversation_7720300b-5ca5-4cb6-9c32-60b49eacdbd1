import type { User } from '../../molecules/UserCard'

export interface NavigationItem {
  id: string
  label: string
  href?: string
  onClick?: () => void
  active?: boolean
  disabled?: boolean
  badge?: string | number
}

export interface NavigationHeaderProps {
  /**
   * Application title/logo
   */
  title?: string

  /**
   * Logo image source
   */
  logoSrc?: string

  /**
   * Navigation items
   */
  navigationItems?: NavigationItem[]

  /**
   * Current user data
   */
  user?: User

  /**
   * Show search bar in header
   */
  showSearch?: boolean

  /**
   * Search placeholder
   */
  searchPlaceholder?: string

  /**
   * Show notifications
   */
  showNotifications?: boolean

  /**
   * Notification count
   */
  notificationCount?: number

  /**
   * Show user menu
   */
  showUserMenu?: boolean

  /**
   * Mobile menu toggle (for responsive design)
   */
  onMobileMenuToggle?: () => void

  /**
   * Search handler
   */
  onSearch?: (query: string) => void

  /**
   * Notification click handler
   */
  onNotificationClick?: () => void

  /**
   * User menu click handler
   */
  onUserMenuClick?: () => void

  /**
   * Logout handler
   */
  onLogout?: () => void

  /**
   * Additional actions to render
   */
  actions?: React.ReactNode
}
