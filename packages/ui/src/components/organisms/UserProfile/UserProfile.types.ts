import type { User } from '../../molecules/UserCard'

export interface UserProfileData extends User {
  bio?: string
  phone?: string
  location?: string
  website?: string
  company?: string
  joinedAt?: Date
  skills?: string[]
  certifications?: string[]
  socialLinks?: {
    linkedin?: string
    github?: string
    twitter?: string
  }
}

export interface UserProfileProps {
  /**
   * User profile data
   */
  user: UserProfileData

  /**
   * Edit mode
   */
  editable?: boolean

  /**
   * Loading state
   */
  loading?: boolean

  /**
   * Show contact information
   */
  showContact?: boolean

  /**
   * Show skills section
   */
  showSkills?: boolean

  /**
   * Show certifications section
   */
  showCertifications?: boolean

  /**
   * Show social links
   */
  showSocialLinks?: boolean

  /**
   * Avatar upload handler
   */
  onAvatarUpload?: (file: File) => void

  /**
   * Profile update handler
   */
  onUpdate?: (updatedUser: Partial<UserProfileData>) => void

  /**
   * Additional actions
   */
  actions?: React.ReactNode
}
