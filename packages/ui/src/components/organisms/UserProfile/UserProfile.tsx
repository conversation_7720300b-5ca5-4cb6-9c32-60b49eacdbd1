import {
  ActionIcon,
  Box,
  Divider,
  Grid,
  Group,
  Paper,
  Skeleton,
  Stack,
  Text
} from '@mantine/core'
import {
  IconBrandGithub,
  IconBrandLinkedin,
  IconBrandTwitter,
  IconBuilding,
  IconCalendar,
  IconEdit,
  IconMail,
  IconMapPin,
  IconPhone,
  IconWorld
} from '@tabler/icons-react'
import { useState } from 'react'
import { Avatar } from '../../atoms/Avatar'
import { Badge } from '../../atoms/Badge'
import { Button } from '../../atoms/Button'
import { Icon } from '../../atoms/Icon'
import type { UserProfileProps } from './UserProfile.types'

/**
 * UserProfile organism for displaying comprehensive user information
 *
 * @example
 * ```tsx
 * <UserProfile
 *   user={{
 *     id: '1',
 *     name: '<PERSON>',
 *     email: '<EMAIL>',
 *     role: 'Senior Developer',
 *     bio: 'Experienced full-stack developer...',
 *     skills: ['React', 'TypeScript', 'Node.js'],
 *     location: 'San Francisco, CA'
 *   }}
 *   editable
 *   showContact
 *   showSkills
 *   onUpdate={handleUpdate}
 * />
 * ```
 */
export function UserProfile({
  user,
  editable = false,
  loading = false,
  showContact = true,
  showSkills = true,
  showCertifications = true,
  showSocialLinks = true,
  onAvatarUpload,
  onUpdate,
  actions,
  ...props
}: UserProfileProps) {
  const [isEditing, setIsEditing] = useState(false)

  if (loading) {
    return (
      <Paper p="xl" withBorder {...props}>
        <Stack gap="xl">
          <Group>
            <Skeleton height={80} circle />
            <Stack gap="xs" style={{ flex: 1 }}>
              <Skeleton height={24} width="40%" />
              <Skeleton height={16} width="60%" />
              <Skeleton height={14} width="30%" />
            </Stack>
          </Group>
          <Skeleton height={60} />
          <Skeleton height={100} />
        </Stack>
      </Paper>
    )
  }

  const formatJoinDate = (date?: Date) => {
    if (!date) return ''
    return `Joined ${date.toLocaleDateString('en-US', {
      month: 'long',
      year: 'numeric'
    })}`
  }

  return (
    <Paper p="xl" withBorder {...props}>
      <Stack gap="xl">
        {/* Header Section */}
        <Group justify="space-between" align="flex-start">
          <Group gap="lg">
            <Box pos="relative">
              <Avatar
                src={user.avatar}
                name={user.name}
                size={80}
                showStatus
                isOnline={user.status === 'online'}
              />
              {editable && onAvatarUpload && (
                <ActionIcon
                  size="sm"
                  variant="filled"
                  pos="absolute"
                  bottom={0}
                  right={0}
                  style={{ borderRadius: '50%' }}
                >
                  <Icon icon={IconEdit} size="xs" />
                </ActionIcon>
              )}
            </Box>

            <Stack gap="xs">
              <Text size="xl" fw={700}>
                {user.name}
              </Text>

              {user.role && (
                <Badge color="primary" size="md">
                  {user.role}
                </Badge>
              )}

              {user.joinedAt && (
                <Group gap="xs">
                  <Icon icon={IconCalendar} size="sm" color="gray" />
                  <Text size="sm" c="dimmed">
                    {formatJoinDate(user.joinedAt)}
                  </Text>
                </Group>
              )}
            </Stack>
          </Group>

          <Group gap="sm">
            {actions}
            {editable && (
              <Button
                variant="outline"
                leftIcon={<Icon icon={IconEdit} size="sm" />}
                onClick={() => setIsEditing(!isEditing)}
              >
                {isEditing ? 'Save' : 'Edit'}
              </Button>
            )}
          </Group>
        </Group>

        {/* Bio Section */}
        {user.bio && (
          <>
            <Divider />
            <Stack gap="sm">
              <Text fw={600}>About</Text>
              <Text size="sm" c="dimmed">
                {user.bio}
              </Text>
            </Stack>
          </>
        )}

        {/* Contact Information */}
        {showContact && (
          <>
            <Divider />
            <Stack gap="sm">
              <Text fw={600}>Contact Information</Text>
              <Grid>
                {user.email && (
                  <Grid.Col span={6}>
                    <Group gap="xs">
                      <Icon icon={IconMail} size="sm" color="gray" />
                      <Text size="sm">{user.email}</Text>
                    </Group>
                  </Grid.Col>
                )}

                {user.phone && (
                  <Grid.Col span={6}>
                    <Group gap="xs">
                      <Icon icon={IconPhone} size="sm" color="gray" />
                      <Text size="sm">{user.phone}</Text>
                    </Group>
                  </Grid.Col>
                )}

                {user.location && (
                  <Grid.Col span={6}>
                    <Group gap="xs">
                      <Icon icon={IconMapPin} size="sm" color="gray" />
                      <Text size="sm">{user.location}</Text>
                    </Group>
                  </Grid.Col>
                )}

                {user.website && (
                  <Grid.Col span={6}>
                    <Group gap="xs">
                      <Icon icon={IconWorld} size="sm" color="gray" />
                      <Text size="sm">{user.website}</Text>
                    </Group>
                  </Grid.Col>
                )}

                {user.company && (
                  <Grid.Col span={6}>
                    <Group gap="xs">
                      <Icon icon={IconBuilding} size="sm" color="gray" />
                      <Text size="sm">{user.company}</Text>
                    </Group>
                  </Grid.Col>
                )}
              </Grid>
            </Stack>
          </>
        )}

        {/* Skills Section */}
        {showSkills && user.skills && user.skills.length > 0 && (
          <>
            <Divider />
            <Stack gap="sm">
              <Text fw={600}>Skills</Text>
              <Group gap="xs">
                {user.skills.map((skill) => (
                  <Badge key={skill} color="secondary" variant="light">
                    {skill}
                  </Badge>
                ))}
              </Group>
            </Stack>
          </>
        )}

        {/* Certifications Section */}
        {showCertifications &&
          user.certifications &&
          user.certifications.length > 0 && (
            <>
              <Divider />
              <Stack gap="sm">
                <Text fw={600}>Certifications</Text>
                <Group gap="xs">
                  {user.certifications.map((cert) => (
                    <Badge key={cert} color="success" variant="light">
                      {cert}
                    </Badge>
                  ))}
                </Group>
              </Stack>
            </>
          )}

        {/* Social Links */}
        {showSocialLinks && user.socialLinks && (
          <>
            <Divider />
            <Stack gap="sm">
              <Text fw={600}>Social Links</Text>
              <Group gap="md">
                {user.socialLinks.linkedin && (
                  <ActionIcon
                    component="a"
                    href={user.socialLinks.linkedin}
                    target="_blank"
                    variant="subtle"
                    color="blue"
                  >
                    <Icon icon={IconBrandLinkedin} size="md" />
                  </ActionIcon>
                )}

                {user.socialLinks.github && (
                  <ActionIcon
                    component="a"
                    href={user.socialLinks.github}
                    target="_blank"
                    variant="subtle"
                    color="gray"
                  >
                    <Icon icon={IconBrandGithub} size="md" />
                  </ActionIcon>
                )}

                {user.socialLinks.twitter && (
                  <ActionIcon
                    component="a"
                    href={user.socialLinks.twitter}
                    target="_blank"
                    variant="subtle"
                    color="blue"
                  >
                    <Icon icon={IconBrandTwitter} size="md" />
                  </ActionIcon>
                )}
              </Group>
            </Stack>
          </>
        )}
      </Stack>
    </Paper>
  )
}
