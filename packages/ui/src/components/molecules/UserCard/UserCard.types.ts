export interface User {
  id: string
  name: string
  email?: string
  avatar?: string
  role?: string
  status?: 'online' | 'offline' | 'away'
  lastSeen?: Date
}

export interface UserCardProps {
  /**
   * User data
   */
  user: User

  /**
   * Show user status indicator
   */
  showStatus?: boolean

  /**
   * Show user role
   */
  showRole?: boolean

  /**
   * Show last seen time
   */
  showLastSeen?: boolean

  /**
   * Card size
   */
  size?: 'sm' | 'md' | 'lg'

  /**
   * Click handler
   */
  onClick?: (user: User) => void

  /**
   * Additional actions (buttons, menu, etc.)
   */
  actions?: React.ReactNode

  /**
   * Loading state
   */
  loading?: boolean
}
