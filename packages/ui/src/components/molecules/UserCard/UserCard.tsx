import { Group, Paper, Skeleton, Stack, Text } from '@mantine/core'
import { Avatar } from '../../atoms/Avatar'
import { Badge } from '../../atoms/Badge'
import type { UserCardProps } from './UserCard.types'

/**
 * UserCard component for displaying user information in a card format
 *
 * @example
 * ```tsx
 * <UserCard
 *   user={{
 *     id: '1',
 *     name: '<PERSON>',
 *     email: '<EMAIL>',
 *     role: 'Owner',
 *     status: 'online'
 *   }}
 *   showStatus
 *   showRole
 *   onClick={handleUserClick}
 * />
 * ```
 */
export function UserCard({
  user,
  showStatus = false,
  showRole = false,
  showLastSeen = false,
  size = 'md',
  onClick,
  actions,
  loading = false,
  ...props
}: UserCardProps) {
  const avatarSize = size === 'sm' ? 'sm' : size === 'lg' ? 'lg' : 'md'
  const padding = size === 'sm' ? 'sm' : size === 'lg' ? 'lg' : 'md'

  if (loading) {
    return (
      <Paper p={padding} withBorder {...props}>
        <Group gap="md">
          <Skeleton
            height={avatarSize === 'sm' ? 32 : avatarSize === 'lg' ? 48 : 40}
            circle
          />
          <Stack gap="xs" style={{ flex: 1 }}>
            <Skeleton height={16} width="60%" />
            <Skeleton height={12} width="80%" />
          </Stack>
        </Group>
      </Paper>
    )
  }

  const getStatusColor = (status?: string) => {
    switch (status) {
      case 'online':
        return 'success'
      case 'away':
        return 'warning'
      case 'offline':
        return 'secondary'
      default:
        return 'secondary'
    }
  }

  const formatLastSeen = (date?: Date) => {
    if (!date) return ''

    const now = new Date()
    const diffMs = now.getTime() - date.getTime()
    const diffMins = Math.floor(diffMs / (1000 * 60))
    const diffHours = Math.floor(diffMins / 60)
    const diffDays = Math.floor(diffHours / 24)

    if (diffMins < 1) return 'Just now'
    if (diffMins < 60) return `${diffMins}m ago`
    if (diffHours < 24) return `${diffHours}h ago`
    if (diffDays < 7) return `${diffDays}d ago`

    return date.toLocaleDateString()
  }

  return (
    <Paper
      p={padding}
      withBorder
      style={{ cursor: onClick ? 'pointer' : undefined }}
      onClick={() => onClick?.(user)}
      {...props}
    >
      <Group justify="space-between" align="flex-start">
        <Group gap="md" style={{ flex: 1 }}>
          <Avatar
            src={user.avatar}
            name={user.name}
            size={avatarSize}
            showStatus={showStatus}
            isOnline={user.status === 'online'}
          />

          <Stack gap="xs" style={{ flex: 1 }}>
            <Text fw={500} size={size === 'sm' ? 'sm' : 'md'}>
              {user.name}
            </Text>

            {user.email && (
              <Text size="sm" c="dimmed">
                {user.email}
              </Text>
            )}

            <Group gap="xs">
              {showRole && user.role && (
                <Badge color="primary" size="xs">
                  {user.role}
                </Badge>
              )}

              {showStatus && user.status && (
                <Badge color={getStatusColor(user.status)} size="xs">
                  {user.status}
                </Badge>
              )}
            </Group>

            {showLastSeen && user.lastSeen && user.status !== 'online' && (
              <Text size="xs" c="dimmed">
                Last seen {formatLastSeen(user.lastSeen)}
              </Text>
            )}
          </Stack>
        </Group>

        {actions && (
          <button
            type="button"
            onClick={(e) => e.stopPropagation()}
            onKeyDown={(e) => {
              if (e.key === 'Enter' || e.key === ' ') {
                e.stopPropagation()
              }
            }}
            tabIndex={0}
          >
            {actions}
          </button>
        )}
      </Group>
    </Paper>
  )
}
