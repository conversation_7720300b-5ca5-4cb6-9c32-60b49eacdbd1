import { ActionIcon, Group } from '@mantine/core'
import { IconSearch, IconX } from '@tabler/icons-react'
import { useEffect, useState } from 'react'
import { Icon } from '../../atoms/Icon'
import { Input } from '../../atoms/Input'
import type { SearchBarProps } from './SearchBar.types'

/**
 * SearchBar component with debounced search and clear functionality
 *
 * @example
 * ```tsx
 * <SearchBar
 *   placeholder="Search projects..."
 *   onSearch={handleSearch}
 *   clearable
 *   debounceMs={300}
 * />
 * ```
 */
export function SearchBar({
  placeholder = 'Search...',
  value: controlledValue,
  onSearch,
  onChange,
  loading = false,
  disabled = false,
  size = 'md',
  clearable = true,
  debounceMs = 300,
  ...props
}: SearchBarProps) {
  const [internalValue, setInternalValue] = useState(controlledValue || '')
  const isControlled = controlledValue !== undefined

  // Use controlled value if provided, otherwise use internal state
  const currentValue = isControlled ? controlledValue : internalValue

  // Debounced search effect
  useEffect(() => {
    if (debounceMs <= 0) {
      onSearch(currentValue)
      return
    }

    const timeoutId = setTimeout(() => {
      onSearch(currentValue)
    }, debounceMs)

    return () => clearTimeout(timeoutId)
  }, [currentValue, onSearch, debounceMs])

  const handleChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = event.target.value

    if (isControlled) {
      onChange?.(newValue)
    } else {
      setInternalValue(newValue)
    }
  }

  const handleClear = () => {
    if (isControlled) {
      onChange?.('')
    } else {
      setInternalValue('')
    }
  }

  const handleKeyDown = (event: React.KeyboardEvent) => {
    if (event.key === 'Enter') {
      onSearch(currentValue)
    }
  }

  return (
    <Group gap="xs" style={{ flex: 1 }}>
      <Input
        placeholder={placeholder}
        value={currentValue}
        onChange={handleChange}
        onKeyDown={handleKeyDown}
        size={size}
        disabled={disabled}
        leftIcon={<Icon icon={IconSearch} size="sm" />}
        rightIcon={
          clearable && currentValue && !loading ? (
            <ActionIcon
              variant="subtle"
              size="sm"
              onClick={handleClear}
              disabled={disabled}
            >
              <Icon icon={IconX} size="xs" />
            </ActionIcon>
          ) : undefined
        }
        style={{ flex: 1 }}
        {...props}
      />
    </Group>
  )
}
