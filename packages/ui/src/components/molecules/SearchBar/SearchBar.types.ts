export interface SearchBarProps {
  /**
   * Placeholder text
   */
  placeholder?: string

  /**
   * Current search value
   */
  value?: string

  /**
   * Search handler
   */
  onSearch: (query: string) => void

  /**
   * Change handler for controlled input
   */
  onChange?: (value: string) => void

  /**
   * Loading state
   */
  loading?: boolean

  /**
   * Disabled state
   */
  disabled?: boolean

  /**
   * Size of the search bar
   */
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl'

  /**
   * Show clear button when there's text
   */
  clearable?: boolean

  /**
   * Debounce delay in milliseconds
   */
  debounceMs?: number
}
