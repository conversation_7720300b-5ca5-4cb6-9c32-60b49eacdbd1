import type { Icon } from '@tabler/icons-react'

export interface StatCardProps {
  /**
   * Card title
   */
  title: string

  /**
   * Main value to display
   */
  value: string | number

  /**
   * Icon component from Tabler icons
   */
  icon?: Icon

  /**
   * Icon color
   */
  iconColor?: string

  /**
   * Optional description or subtitle
   */
  description?: string

  /**
   * Trend indicator
   */
  trend?: {
    value: number
    label?: string
    isPositive?: boolean
  }

  /**
   * Loading state
   */
  loading?: boolean

  /**
   * Click handler
   */
  onClick?: () => void

  /**
   * Additional CSS class
   */
  className?: string
}
