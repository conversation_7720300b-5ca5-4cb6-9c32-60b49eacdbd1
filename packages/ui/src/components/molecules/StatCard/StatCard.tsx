import { Group, Paper, Skeleton, Stack, Text } from '@mantine/core'
import { IconTrendingDown, IconTrendingUp } from '@tabler/icons-react'
import { Icon } from '../../atoms/Icon'
import type { StatCardProps } from './StatCard.types'

/**
 * StatCard component for displaying key metrics with optional trend indicators
 *
 * @example
 * ```tsx
 * <StatCard
 *   title="Total Projects"
 *   value={42}
 *   icon={IconBriefcase}
 *   iconColor="blue"
 *   trend={{ value: 12, isPositive: true, label: "vs last month" }}
 * />
 * ```
 */
export function StatCard({
  title,
  value,
  icon: IconComponent,
  iconColor = 'blue',
  description,
  trend,
  loading = false,
  onClick,
  className,
  ...props
}: StatCardProps) {
  if (loading) {
    return (
      <Paper p="md" withBorder className={className} {...props}>
        <Group justify="space-between">
          <Stack gap="xs" style={{ flex: 1 }}>
            <Skeleton height={12} width="60%" />
            <Skeleton height={24} width="40%" />
            {description && <Skeleton height={10} width="80%" />}
          </Stack>
          <Skeleton height={24} width={24} />
        </Group>
      </Paper>
    )
  }

  return (
    <Paper
      p="md"
      withBorder
      className={className}
      style={{ cursor: onClick ? 'pointer' : undefined }}
      onClick={onClick}
      {...props}
    >
      <Group justify="space-between" align="flex-start">
        <Stack gap="xs" style={{ flex: 1 }}>
          <Text size="xs" c="dimmed" tt="uppercase" fw={700}>
            {title}
          </Text>

          <Text fw={700} size="xl">
            {typeof value === 'number' ? value.toLocaleString() : value}
          </Text>

          {description && (
            <Text size="sm" c="dimmed">
              {description}
            </Text>
          )}

          {trend && (
            <Group gap="xs">
              <Icon
                icon={trend.isPositive ? IconTrendingUp : IconTrendingDown}
                size="xs"
                color={trend.isPositive ? 'green' : 'red'}
              />
              <Text size="xs" c={trend.isPositive ? 'green' : 'red'} fw={500}>
                {trend.value > 0 ? '+' : ''}
                {trend.value}%
                {trend.label && (
                  <Text component="span" c="dimmed" ml={4}>
                    {trend.label}
                  </Text>
                )}
              </Text>
            </Group>
          )}
        </Stack>

        {IconComponent && (
          <Icon
            icon={IconComponent}
            size="lg"
            color={`var(--mantine-color-${iconColor}-6)`}
          />
        )}
      </Group>
    </Paper>
  )
}
