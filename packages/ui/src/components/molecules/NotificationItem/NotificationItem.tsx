import { ActionIcon, Group, Paper, Stack, Text } from '@mantine/core'
import {
  IconAlertTriangle,
  IconCheck,
  IconExternalLink,
  IconInfoCircle,
  IconX
} from '@tabler/icons-react'
import { Button } from '../../atoms/Button'
import { Icon } from '../../atoms/Icon'
import type { NotificationItemProps } from './NotificationItem.types'

/**
 * NotificationItem component for displaying notifications with actions
 *
 * @example
 * ```tsx
 * <NotificationItem
 *   notification={{
 *     id: '1',
 *     title: 'Project Updated',
 *     message: 'Your project "Website Redesign" has been updated',
 *     type: 'info',
 *     timestamp: new Date(),
 *     actionUrl: '/projects/1',
 *     actionLabel: 'View Project'
 *   }}
 *   onMarkAsRead={handleMarkAsRead}
 *   onActionClick={handleActionClick}
 *   showAction
 * />
 * ```
 */
export function NotificationItem({
  notification,
  icon: customIcon,
  onClick,
  onMarkAsRead,
  onDismiss,
  onActionClick,
  showDismiss = true,
  showAction = true,
  compact = false,
  ...props
}: NotificationItemProps) {
  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'success':
        return IconCheck
      case 'warning':
        return IconAlertTriangle
      case 'error':
        return IconX
      default:
        return IconInfoCircle
    }
  }

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'success':
        return 'green'
      case 'warning':
        return 'yellow'
      case 'error':
        return 'red'
      default:
        return 'blue'
    }
  }

  const formatTimestamp = (date: Date) => {
    const now = new Date()
    const diffMs = now.getTime() - date.getTime()
    const diffMins = Math.floor(diffMs / (1000 * 60))
    const diffHours = Math.floor(diffMins / 60)
    const diffDays = Math.floor(diffHours / 24)

    if (diffMins < 1) return 'Just now'
    if (diffMins < 60) return `${diffMins}m ago`
    if (diffHours < 24) return `${diffHours}h ago`
    if (diffDays < 7) return `${diffDays}d ago`

    return date.toLocaleDateString()
  }

  const IconComponent = customIcon || getTypeIcon(notification.type)
  const iconColor = getTypeColor(notification.type)
  const padding = compact ? 'sm' : 'md'

  return (
    <Paper
      p={padding}
      withBorder
      style={{
        cursor: onClick ? 'pointer' : undefined,
        opacity: notification.read ? 0.7 : 1,
        borderLeft: `3px solid var(--mantine-color-${iconColor}-6)`
      }}
      onClick={() => onClick?.(notification)}
      {...props}
    >
      <Group align="flex-start" gap="md">
        <Icon
          icon={IconComponent}
          size={compact ? 'sm' : 'md'}
          color={`var(--mantine-color-${iconColor}-6)`}
        />

        <Stack gap="xs" style={{ flex: 1 }}>
          <Group justify="space-between" align="flex-start">
            <Stack gap="xs" style={{ flex: 1 }}>
              <Text
                fw={notification.read ? 400 : 600}
                size={compact ? 'sm' : 'md'}
                lineClamp={1}
              >
                {notification.title}
              </Text>

              <Text
                size={compact ? 'xs' : 'sm'}
                c="dimmed"
                lineClamp={compact ? 1 : 2}
              >
                {notification.message}
              </Text>

              <Text size="xs" c="dimmed">
                {formatTimestamp(notification.timestamp)}
              </Text>
            </Stack>

            {showDismiss && onDismiss && (
              <ActionIcon
                variant="subtle"
                size="sm"
                onClick={(e) => {
                  e.stopPropagation()
                  onDismiss(notification.id)
                }}
              >
                <Icon icon={IconX} size="xs" />
              </ActionIcon>
            )}
          </Group>

          {/* Actions */}
          <Group gap="xs">
            {!notification.read && onMarkAsRead && (
              <Button
                variant="subtle"
                size="xs"
                onClick={(e: React.MouseEvent) => {
                  e.stopPropagation()
                  onMarkAsRead(notification.id)
                }}
              >
                Mark as read
              </Button>
            )}

            {showAction && notification.actionLabel && (
              <Button
                variant="outline"
                size="xs"
                rightSection={<Icon icon={IconExternalLink} size="xs" />}
                onClick={(e: React.MouseEvent) => {
                  e.stopPropagation()
                  onActionClick?.(notification)
                }}
              >
                {notification.actionLabel}
              </Button>
            )}
          </Group>
        </Stack>
      </Group>
    </Paper>
  )
}
