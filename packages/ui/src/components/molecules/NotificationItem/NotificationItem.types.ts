import type { Icon } from '@tabler/icons-react'

export interface Notification {
  id: string
  title: string
  message: string
  type: 'info' | 'success' | 'warning' | 'error'
  timestamp: Date
  read?: boolean
  actionUrl?: string
  actionLabel?: string
}

export interface NotificationItemProps {
  /**
   * Notification data
   */
  notification: Notification

  /**
   * Custom icon override
   */
  icon?: Icon

  /**
   * Click handler for the notification
   */
  onClick?: (notification: Notification) => void

  /**
   * Mark as read handler
   */
  onMarkAsRead?: (notificationId: string) => void

  /**
   * Dismiss handler
   */
  onDismiss?: (notificationId: string) => void

  /**
   * Action button click handler
   */
  onActionClick?: (notification: Notification) => void

  /**
   * Show dismiss button
   */
  showDismiss?: boolean

  /**
   * Show action button
   */
  showAction?: boolean

  /**
   * Compact mode (smaller padding and text)
   */
  compact?: boolean
}
