export interface Project {
  id: string
  title: string
  description?: string
  status: 'draft' | 'active' | 'completed' | 'cancelled' | 'pending'
  budget?: number
  deadline?: Date
  location?: string
  contractorCount?: number
  progress?: number
  tags?: string[]
  createdAt?: Date
  updatedAt?: Date
}

export interface ProjectCardProps {
  /**
   * Project data
   */
  project: Project

  /**
   * Show project progress bar
   */
  showProgress?: boolean

  /**
   * Show project budget
   */
  showBudget?: boolean

  /**
   * Show project deadline
   */
  showDeadline?: boolean

  /**
   * Show project location
   */
  showLocation?: boolean

  /**
   * Show contractor count
   */
  showContractorCount?: boolean

  /**
   * Show project tags
   */
  showTags?: boolean

  /**
   * Card size
   */
  size?: 'sm' | 'md' | 'lg'

  /**
   * Click handler
   */
  onClick?: (project: Project) => void

  /**
   * Additional actions (buttons, menu, etc.)
   */
  actions?: React.ReactNode

  /**
   * Loading state
   */
  loading?: boolean
}
