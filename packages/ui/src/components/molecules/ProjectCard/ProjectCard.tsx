import { Group, Paper, Progress, Skeleton, Stack, Text } from '@mantine/core'
import {
  IconCalendar,
  IconCurrencyDollar,
  IconMapPin,
  IconUsers
} from '@tabler/icons-react'
import { Badge } from '../../atoms/Badge'
import { Icon } from '../../atoms/Icon'
import type { ProjectCardProps } from './ProjectCard.types'

/**
 * ProjectCard component for displaying project information in a card format
 *
 * @example
 * ```tsx
 * <ProjectCard
 *   project={{
 *     id: '1',
 *     title: 'Website Redesign',
 *     description: 'Complete redesign of company website',
 *     status: 'active',
 *     budget: 5000,
 *     progress: 65
 *   }}
 *   showProgress
 *   showBudget
 *   onClick={handleProjectClick}
 * />
 * ```
 */
export function ProjectCard({
  project,
  showProgress = false,
  showBudget = false,
  showDeadline = false,
  showLocation = false,
  showContractorCount = false,
  showTags = false,
  size = 'md',
  onClick,
  actions,
  loading = false,
  ...props
}: ProjectCardProps) {
  const padding = size === 'sm' ? 'sm' : size === 'lg' ? 'lg' : 'md'

  if (loading) {
    return (
      <Paper p={padding} withBorder {...props}>
        <Stack gap="md">
          <Group justify="space-between">
            <Skeleton height={20} width="60%" />
            <Skeleton height={20} width={80} />
          </Group>
          <Skeleton height={40} />
          <Group gap="xs">
            <Skeleton height={16} width={60} />
            <Skeleton height={16} width={80} />
          </Group>
        </Stack>
      </Paper>
    )
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'success'
      case 'completed':
        return 'info'
      case 'pending':
        return 'warning'
      case 'cancelled':
        return 'error'
      case 'draft':
        return 'secondary'
      default:
        return 'secondary'
    }
  }

  const formatBudget = (amount?: number) => {
    if (!amount) return ''
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount)
  }

  const formatDate = (date?: Date) => {
    if (!date) return ''
    return date.toLocaleDateString()
  }

  const isOverdue = (deadline?: Date) => {
    if (!deadline) return false
    return deadline < new Date()
  }

  return (
    <Paper
      p={padding}
      withBorder
      style={{ cursor: onClick ? 'pointer' : undefined }}
      onClick={() => onClick?.(project)}
      {...props}
    >
      <Stack gap="md">
        {/* Header */}
        <Group justify="space-between" align="flex-start">
          <Stack gap="xs" style={{ flex: 1 }}>
            <Text fw={500} size={size === 'sm' ? 'sm' : 'md'} lineClamp={1}>
              {project.title}
            </Text>

            <Badge color={getStatusColor(project.status)} size="xs">
              {project.status.charAt(0).toUpperCase() + project.status.slice(1)}
            </Badge>
          </Stack>

          {actions && (
            <button
              type="button"
              style={{
                background: 'none',
                border: 'none',
                padding: 0,
                margin: 0,
                cursor: 'auto'
              }}
              onClick={(e) => e.stopPropagation()}
              onKeyDown={(e) => {
                if (e.key === 'Enter' || e.key === ' ') {
                  e.stopPropagation()
                }
              }}
            >
              {actions}
            </button>
          )}
        </Group>

        {/* Description */}
        {project.description && (
          <Text size="sm" c="dimmed" lineClamp={2}>
            {project.description}
          </Text>
        )}

        {/* Progress */}
        {showProgress && project.progress !== undefined && (
          <Stack gap="xs">
            <Group justify="space-between">
              <Text size="xs" c="dimmed">
                Progress
              </Text>
              <Text size="xs" fw={500}>
                {project.progress}%
              </Text>
            </Group>
            <Progress value={project.progress} size="sm" />
          </Stack>
        )}

        {/* Project Details */}
        <Group gap="md" wrap="wrap">
          {showBudget && project.budget && (
            <Group gap="xs">
              <Icon icon={IconCurrencyDollar} size="xs" color="gray" />
              <Text size="xs" c="dimmed">
                {formatBudget(project.budget)}
              </Text>
            </Group>
          )}

          {showDeadline && project.deadline && (
            <Group gap="xs">
              <Icon
                icon={IconCalendar}
                size="xs"
                color={isOverdue(project.deadline) ? 'red' : 'gray'}
              />
              <Text
                size="xs"
                c={isOverdue(project.deadline) ? 'red' : 'dimmed'}
              >
                {formatDate(project.deadline)}
              </Text>
            </Group>
          )}

          {showLocation && project.location && (
            <Group gap="xs">
              <Icon icon={IconMapPin} size="xs" color="gray" />
              <Text size="xs" c="dimmed">
                {project.location}
              </Text>
            </Group>
          )}

          {showContractorCount && project.contractorCount !== undefined && (
            <Group gap="xs">
              <Icon icon={IconUsers} size="xs" color="gray" />
              <Text size="xs" c="dimmed">
                {project.contractorCount} contractor
                {project.contractorCount !== 1 ? 's' : ''}
              </Text>
            </Group>
          )}
        </Group>

        {/* Tags */}
        {showTags && project.tags && project.tags.length > 0 && (
          <Group gap="xs">
            {project.tags.slice(0, 3).map((tag) => (
              <Badge key={tag} color="secondary" size="xs" variant="light">
                {tag}
              </Badge>
            ))}
            {project.tags.length > 3 && (
              <Text size="xs" c="dimmed">
                +{project.tags.length - 3} more
              </Text>
            )}
          </Group>
        )}
      </Stack>
    </Paper>
  )
}
