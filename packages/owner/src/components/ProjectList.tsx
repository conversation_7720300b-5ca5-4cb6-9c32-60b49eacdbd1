import { formatDate } from '@shared'
import {
  ActionIcon,
  Badge,
  Button,
  Card,
  Container,
  Group,
  IconEdit,
  IconEye,
  IconTrash,
  Stack,
  Text,
  Title
} from '@ui'
import type { Project } from '../types'

interface ProjectListProps {
  projects: Project[]
  isLoading?: boolean
  onEdit?: (project: Project) => void
  onDelete?: (projectId: string) => void
  onView?: (project: Project) => void
}

export function ProjectList({
  projects,
  isLoading = false,
  onEdit,
  onDelete,
  onView
}: ProjectListProps) {
  const getStatusColor = (status: Project['status']) => {
    switch (status) {
      case 'active':
        return 'green'
      case 'completed':
        return 'blue'
      case 'cancelled':
        return 'red'
      case 'draft':
        return 'gray'
      default:
        return 'gray'
    }
  }

  if (isLoading) {
    return (
      <Container size="xl" py="xl">
        <Stack gap="md">
          <Title order={2}>Projects</Title>
          {Array.from({ length: 3 }).map((_, index) => (
            <Card key={index} withBorder>
              <Text>Loading...</Text>
            </Card>
          ))}
        </Stack>
      </Container>
    )
  }

  if (projects.length === 0) {
    return (
      <Container size="xl" py="xl">
        <Stack gap="md">
          <Title order={2}>Projects</Title>
          <Card withBorder>
            <Text c="dimmed" ta="center" py="xl">
              No projects found. Create your first project to get started.
            </Text>
          </Card>
        </Stack>
      </Container>
    )
  }

  return (
    <Container size="xl" py="xl">
      <Stack gap="md">
        <Group justify="space-between">
          <Title order={2}>Projects</Title>
          <Button>Create Project</Button>
        </Group>

        {projects.map((project) => (
          <Card key={project.id} withBorder>
            <Group justify="space-between" align="flex-start">
              <Stack gap="xs" style={{ flex: 1 }}>
                <Group gap="sm">
                  <Title order={4}>{project.name}</Title>
                  <Badge color={getStatusColor(project.status)} variant="light">
                    {project.status}
                  </Badge>
                </Group>

                <Text c="dimmed" size="sm">
                  {project.description}
                </Text>

                <Group gap="md">
                  <Text size="sm">
                    <strong>Budget:</strong> ${project.budget.toLocaleString()}
                  </Text>
                  <Text size="sm">
                    <strong>Start Date:</strong> {formatDate(project.startDate)}
                  </Text>
                  {project.endDate && (
                    <Text size="sm">
                      <strong>End Date:</strong> {formatDate(project.endDate)}
                    </Text>
                  )}
                </Group>
              </Stack>

              <Group gap="xs">
                {onView && (
                  <ActionIcon
                    variant="subtle"
                    color="blue"
                    onClick={() => onView(project)}
                  >
                    <IconEye size={16} />
                  </ActionIcon>
                )}
                {onEdit && (
                  <ActionIcon
                    variant="subtle"
                    color="yellow"
                    onClick={() => onEdit(project)}
                  >
                    <IconEdit size={16} />
                  </ActionIcon>
                )}
                {onDelete && (
                  <ActionIcon
                    variant="subtle"
                    color="red"
                    onClick={() => onDelete(project.id)}
                  >
                    <IconTrash size={16} />
                  </ActionIcon>
                )}
              </Group>
            </Group>
          </Card>
        ))}
      </Stack>
    </Container>
  )
}
