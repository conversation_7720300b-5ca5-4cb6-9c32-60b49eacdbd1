import {
  Card,
  Container,
  Grid,
  Group,
  IconBriefcase,
  IconCurrencyDollar,
  IconTrendingUp,
  IconUsers,
  Paper,
  SimpleGrid,
  Stack,
  Text,
  Title
} from '@ui'
import type { OwnerDashboardStats } from '../types'

interface OwnerDashboardProps {
  stats: OwnerDashboardStats
  isLoading?: boolean
}

export function OwnerDashboard({
  stats,
  isLoading = false
}: OwnerDashboardProps) {
  const statCards = [
    {
      title: 'Total Projects',
      value: stats.totalProjects,
      icon: IconBriefcase,
      color: 'blue'
    },
    {
      title: 'Active Projects',
      value: stats.activeProjects,
      icon: IconTrendingUp,
      color: 'green'
    },
    {
      title: 'Total Contractors',
      value: stats.totalContractors,
      icon: IconUsers,
      color: 'violet'
    },
    {
      title: 'Total Budget',
      value: `$${stats.totalBudget.toLocaleString()}`,
      icon: IconCurrencyDollar,
      color: 'orange'
    }
  ]

  if (isLoading) {
    return (
      <Container size="xl" py="xl">
        <Stack gap="xl">
          <Title order={1}>Owner Dashboard</Title>
          <SimpleGrid cols={{ base: 1, sm: 2, lg: 4 }}>
            {Array.from({ length: 4 }).map((_, index) => (
              <Paper key={index} p="md" withBorder>
                <Group justify="space-between">
                  <div>
                    <Text size="xs" c="dimmed" tt="uppercase" fw={700}>
                      Loading...
                    </Text>
                    <Text fw={700} size="xl">
                      --
                    </Text>
                  </div>
                </Group>
              </Paper>
            ))}
          </SimpleGrid>
        </Stack>
      </Container>
    )
  }

  return (
    <Container size="xl" py="xl">
      <Stack gap="xl">
        <Title order={1}>Owner Dashboard</Title>

        <SimpleGrid cols={{ base: 1, sm: 2, lg: 4 }}>
          {statCards.map((stat) => (
            <Paper key={stat.title} p="md" withBorder>
              <Group justify="space-between">
                <div>
                  <Text size="xs" c="dimmed" tt="uppercase" fw={700}>
                    {stat.title}
                  </Text>
                  <Text fw={700} size="xl">
                    {stat.value}
                  </Text>
                </div>
                <stat.icon
                  size={24}
                  color={`var(--mantine-color-${stat.color}-6)`}
                />
              </Group>
            </Paper>
          ))}
        </SimpleGrid>

        <Grid>
          <Grid.Col span={{ base: 12, md: 8 }}>
            <Card withBorder>
              <Card.Section p="md">
                <Title order={3}>Recent Projects</Title>
              </Card.Section>
              <Card.Section p="md">
                <Text c="dimmed">Project list will be implemented here</Text>
              </Card.Section>
            </Card>
          </Grid.Col>

          <Grid.Col span={{ base: 12, md: 4 }}>
            <Card withBorder>
              <Card.Section p="md">
                <Title order={3}>Quick Actions</Title>
              </Card.Section>
              <Card.Section p="md">
                <Text c="dimmed">
                  Quick action buttons will be implemented here
                </Text>
              </Card.Section>
            </Card>
          </Grid.Col>
        </Grid>
      </Stack>
    </Container>
  )
}
