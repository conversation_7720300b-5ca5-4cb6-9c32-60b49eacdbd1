import { formatDateTime } from '@shared'
import {
  Badge,
  Card,
  Container,
  Grid,
  Group,
  IconBriefcase,
  IconCurrencyDollar,
  IconServer,
  IconUsers,
  Paper,
  SimpleGrid,
  Stack,
  Text,
  Title
} from '@ui'
import type { AdminDashboardStats } from '../types'

interface AdminDashboardProps {
  stats: AdminDashboardStats
  isLoading?: boolean
}

export function AdminDashboard({
  stats,
  isLoading = false
}: AdminDashboardProps) {
  const systemCards = [
    {
      title: 'Total Users',
      value: stats.systemMetrics.totalUsers,
      icon: IconUsers,
      color: 'blue'
    },
    {
      title: 'Active Projects',
      value: stats.systemMetrics.activeProjects,
      icon: IconBriefcase,
      color: 'green'
    },
    {
      title: 'Total Revenue',
      value: `$${stats.systemMetrics.totalRevenue.toLocaleString()}`,
      icon: IconCurrencyDollar,
      color: 'orange'
    },
    {
      title: 'System Uptime',
      value: `${stats.systemMetrics.systemUptime.toFixed(1)}%`,
      icon: IconServer,
      color: 'violet'
    }
  ]

  if (isLoading) {
    return (
      <Container size="xl" py="xl">
        <Stack gap="xl">
          <Title order={1}>Admin Dashboard</Title>
          <SimpleGrid cols={{ base: 1, sm: 2, lg: 4 }}>
            {Array.from({ length: 4 }).map((_, index) => (
              <Paper key={index} p="md" withBorder>
                <Group justify="space-between">
                  <div>
                    <Text size="xs" c="dimmed" tt="uppercase" fw={700}>
                      Loading...
                    </Text>
                    <Text fw={700} size="xl">
                      --
                    </Text>
                  </div>
                </Group>
              </Paper>
            ))}
          </SimpleGrid>
        </Stack>
      </Container>
    )
  }

  return (
    <Container size="xl" py="xl">
      <Stack gap="xl">
        <Title order={1}>Admin Dashboard</Title>

        <SimpleGrid cols={{ base: 1, sm: 2, lg: 4 }}>
          {systemCards.map((stat) => (
            <Paper key={stat.title} p="md" withBorder>
              <Group justify="space-between">
                <div>
                  <Text size="xs" c="dimmed" tt="uppercase" fw={700}>
                    {stat.title}
                  </Text>
                  <Text fw={700} size="xl">
                    {stat.value}
                  </Text>
                </div>
                <stat.icon
                  size={24}
                  color={`var(--mantine-color-${stat.color}-6)`}
                />
              </Group>
            </Paper>
          ))}
        </SimpleGrid>

        <Grid>
          <Grid.Col span={{ base: 12, md: 6 }}>
            <Card withBorder>
              <Card.Section p="md">
                <Title order={3}>User Statistics</Title>
              </Card.Section>
              <Card.Section p="md">
                <Stack gap="sm">
                  <Group justify="space-between">
                    <Text>Owners</Text>
                    <Badge variant="light" color="blue">
                      {stats.userStats.totalOwners}
                    </Badge>
                  </Group>
                  <Group justify="space-between">
                    <Text>Contractors</Text>
                    <Badge variant="light" color="green">
                      {stats.userStats.totalContractors}
                    </Badge>
                  </Group>
                  <Group justify="space-between">
                    <Text>Admins</Text>
                    <Badge variant="light" color="violet">
                      {stats.userStats.totalAdmins}
                    </Badge>
                  </Group>
                  <Group justify="space-between">
                    <Text>New This Month</Text>
                    <Badge variant="light" color="orange">
                      {stats.userStats.newUsersThisMonth}
                    </Badge>
                  </Group>
                </Stack>
              </Card.Section>
            </Card>
          </Grid.Col>

          <Grid.Col span={{ base: 12, md: 6 }}>
            <Card withBorder>
              <Card.Section p="md">
                <Title order={3}>Recent Activity</Title>
              </Card.Section>
              <Card.Section p="md">
                <Stack gap="xs">
                  {stats.recentActivity.slice(0, 5).map((activity) => (
                    <Group key={activity.id} justify="space-between">
                      <div>
                        <Text size="sm" fw={500}>
                          {activity.userName}
                        </Text>
                        <Text size="xs" c="dimmed">
                          {activity.action} {activity.resource}
                        </Text>
                      </div>
                      <Text size="xs" c="dimmed">
                        {formatDateTime(activity.createdAt)}
                      </Text>
                    </Group>
                  ))}
                </Stack>
              </Card.Section>
            </Card>
          </Grid.Col>
        </Grid>
      </Stack>
    </Container>
  )
}
