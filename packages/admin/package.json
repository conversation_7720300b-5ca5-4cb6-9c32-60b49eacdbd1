{"name": "@admin", "version": "0.0.0", "type": "module", "main": "./src/index.ts", "module": "./src/index.ts", "types": "./src/index.ts", "exports": {".": {"import": "./src/index.ts", "types": "./src/index.ts"}}, "scripts": {"check-types": "tsc --noEmit"}, "peerDependencies": {"react": "^19.0.0", "react-dom": "^19.0.0"}, "dependencies": {"@shared": "workspace:*", "@ui": "workspace:*", "@tanstack/react-router": "^1.114.25", "@tanstack/react-query": "^5.80.5", "zod": "^3.25.67"}, "devDependencies": {"@types/react": "^19.0.12", "@types/react-dom": "^19.0.4", "react": "^19.0.0", "react-dom": "^19.0.0", "typescript": "^5.8.2"}}