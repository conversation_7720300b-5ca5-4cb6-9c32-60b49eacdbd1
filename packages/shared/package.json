{"name": "@shared", "version": "0.0.0", "type": "module", "main": "./src/index.ts", "module": "./src/index.ts", "types": "./src/index.ts", "exports": {".": {"import": "./src/index.ts", "types": "./src/index.ts"}}, "scripts": {"check-types": "tsc --noEmit"}, "peerDependencies": {"react": "^19.0.0", "react-dom": "^19.0.0"}, "dependencies": {"@tanstack/react-query": "^5.80.5", "@tanstack/react-router": "^1.114.25", "aws-amplify": "^6.15.1", "axios": "^1.10.0", "dayjs": "^1.11.13", "jotai": "^2.12.5", "sonner": "^2.0.5", "zod": "^3.25.67"}, "devDependencies": {"@types/react": "^19.0.12", "@types/react-dom": "^19.0.4", "react": "^19.0.0", "react-dom": "^19.0.0", "typescript": "^5.8.2", "vite": "^6.2.2"}}