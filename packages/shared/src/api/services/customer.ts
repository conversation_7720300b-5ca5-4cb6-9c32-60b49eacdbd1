/**
 * Customer API service
 * Services for managing customers
 */

import type {
  Customer,
  CustomerListResponse,
  GetCustomerDetailRequest,
  GetCustomerListRequest,
  ServerResponse
} from '@/libs/types'
import type {
  CreateCustomerRequest,
  CustomerDetailResponse,
  GetCustomersParams,
  UpdateCustomerApiResponse
} from '@/libs/types/api/customer'
import { API_ENDPOINTS, request } from '../core'

/**
 * Customer API service
 */
export const customerService = {
  /**
   * Get list of customers - New API
   */
  getListCustomers: async (params: GetCustomerListRequest) => {
    const res = await request<CustomerListResponse>(
      API_ENDPOINTS.CUSTOMER.LIST.buildUrl(),
      { params }
    )
    return res.data
  },

  /**
   * Get customer detail - supports both direct ID and object param
   */
  getCustomerDetail: async (
    param: GetCustomerDetailRequest | string | number
  ) => {
    let customerId: string | number
    if (typeof param === 'object') {
      customerId = param.customerId
    } else {
      customerId = param
    }

    const res = await request<CustomerDetailResponse>(
      API_ENDPOINTS.CUSTOMER.DETAIL.buildUrl({ customerId })
    )
    return res
  },

  /**
   * Get list of customers - Legacy API (uses getListCustomers internally)
   * @deprecated Use getListCustomers instead
   */
  getCustomers: async (params: GetCustomersParams) => {
    return customerService.getListCustomers(params)
  },

  /**
   * Create new customer
   */
  createCustomer: async (data: CreateCustomerRequest) => {
    const res = await request.post<ServerResponse<Customer>>(
      API_ENDPOINTS.CUSTOMER.CREATE.buildUrl(),
      data
    )
    return res.data
  },

  /**
   * Update existing customer
   */
  updateCustomer: async (
    customerId: string | number,
    data: CreateCustomerRequest
  ) => {
    const res = await request.put<UpdateCustomerApiResponse>(
      API_ENDPOINTS.CUSTOMER.UPDATE.buildUrl({ customerId }),
      data
    )
    return res.data
  }
}
