/**
 * Conversation API service
 * Services for managing group conversations
 */

import type {
  ConversationDetailResponse,
  GetConversationMessagesRequest,
  GetListSellerConversationRequest,
  GetListSolicitorConversationRequest,
  GetUnreadMessageResponse,
  MessageListResponse,
  SellerConversationListResponse,
  SendMessageRequest,
  SolicitorConversationListResponse,
  UpdateSolicitorInfoRequest,
  UpdateUnreadCountRequest
} from '@/libs/types'
import { extractUrlParams } from '@/libs/utils/helper'
import { API_ENDPOINTS, request, type ServerResponse } from '../core'

/**
 * Conversation API service
 */
export const grConversationService = {
  /**
   * Get list of conversations
   */
  getListSellerConversations: async ({
    customerId,
    ...params
  }: GetListSellerConversationRequest) => {
    const res = await request<SellerConversationListResponse>(
      API_ENDPOINTS.CONVERSATION.LIST_SELLER.buildUrl({ customerId }),
      { params }
    )
    return res.data
  },

  /**
   * Get list of conversations
   */
  getListSolicitorConversations: async (
    params: GetListSolicitorConversationRequest
  ) => {
    const res = await request<SolicitorConversationListResponse>(
      API_ENDPOINTS.CONVERSATION.LIST_SOLICITOR.buildUrl(),
      { params }
    )
    return res.data
  },

  /**
   * Get conversation detail
   */
  getConversationDetail: async (conversationId: string | number) => {
    const res = await request<ConversationDetailResponse>(
      API_ENDPOINTS.CONVERSATION.DETAIL.buildUrl({ conversationId })
    )
    return res.data
  },

  /**
   * Get conversation messages
   */
  getMessages: async ({
    conversationId,
    nextPageUrl
  }: GetConversationMessagesRequest) => {
    const params = extractUrlParams(nextPageUrl)

    const endpoint = API_ENDPOINTS.CONVERSATION.MESSAGES.LIST.buildUrl({
      conversationId
    })

    const res = await request<MessageListResponse>(endpoint, { params })
    return res.data
  },

  /**
   * Send message in conversation
   */
  sendMessage: async ({ conversationId, content }: SendMessageRequest) => {
    const res = await request.post<ServerResponse<{ id: number }>>(
      API_ENDPOINTS.CONVERSATION.MESSAGES.SEND.buildUrl({ conversationId }),
      { content }
    )
    return res.data
  },

  /**
   * Update unread message count
   */
  updateUnreadCount: async ({ conversationId }: UpdateUnreadCountRequest) => {
    const res = await request.patch<ServerResponse>(
      API_ENDPOINTS.CONVERSATION.MESSAGES.UPDATE_UNREAD.buildUrl({
        conversationId
      })
    )
    return res.data
  },

  /**
   * Get unread message
   */
  getUnreadMessage: async (
    conversationId: string,
    params: Record<string, unknown> = {}
  ) => {
    const res = await request.get<GetUnreadMessageResponse>(
      API_ENDPOINTS.CONVERSATION.MESSAGES.UNREAD.buildUrl({
        conversationId
      }),
      { params }
    )
    return res
  },

  /**
   * Update solicitor info
   */
  updateSolicitorInfo: async ({
    conversationId,
    ...body
  }: UpdateSolicitorInfoRequest) => {
    const res = await request.post<ServerResponse>(
      API_ENDPOINTS.CONVERSATION.UPDATE_SOLICITOR_CONVERSATION_INFO.buildUrl({
        conversationId
      }),
      body
    )

    return res
  }
}
