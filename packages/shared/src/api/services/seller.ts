// MARK: Seller
/**
 * Seller API service
 * Services for managing seller data and options
 */

import type {
  GetSellerCompanyDepartmentParamType,
  GetSellerCompanyResponse,
  GetSellerCompanyUserParamType,
  GetSellerDepartmentsResponse,
  GetSellerUsersResponse
} from '@/libs/types/api/seller'
import { API_ENDPOINTS, request } from '../core'

/**
 * Type definition for seller company
 */
export interface SellerCompanyType {
  company_name: string
  company_avatar_url: string
  company_id: number
  // user_id: number
  // user_name: string
  // user_avatar_url: string
}

/**
 * Type definition for seller user
 */
export interface SellerUserType {
  company_name: string
  company_avatar_url: string
  company_id: number
  user_id: number
  user_name: string
  user_avatar_url: string
}

/**
 * Seller API service
 */
export const sellerService = {
  /**
   * Get company seller
   */
  getSellerCompany: async () => {
    const res = await request<GetSellerCompanyResponse>(
      API_ENDPOINTS.SELLER.COMPANY.buildUrl()
    )
    return res.data
  },

  getSellerCompanyDepartment: async (
    params: GetSellerCompanyDepartmentParamType
  ) => {
    const res = await request<GetSellerDepartmentsResponse>(
      API_ENDPOINTS.SELLER.DEPARTMENT.buildUrl(),
      { params }
    )
    return res.data
  },

  /**
   * Get solicitor users
   */
  getSellerUsers: async (params: GetSellerCompanyUserParamType) => {
    const res = await request<GetSellerUsersResponse>(
      API_ENDPOINTS.SELLER.USERS.buildUrl(),
      { params }
    )

    return res.data
  }
}
