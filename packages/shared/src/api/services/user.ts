/**
 * User API service
 * Services for managing user data and options
 */

import { queryOptions } from '@tanstack/react-query'
import type { GetCurrentUserResponse } from '@/libs/types'
import { API_ENDPOINTS, request } from '../core'

/**
 * Type definition for user response
 */

export const userService = {
  /**
   * Get current user information
   */
  getMe: async () => {
    const res = await request<GetCurrentUserResponse>(
      API_ENDPOINTS.USER.ME.buildUrl()
    )
    return res.data
  }
}

export const getUserQueryOptions = (options = { enabled: true }) => {
  return queryOptions({
    queryKey: [API_ENDPOINTS.USER.ME.queryKey],
    queryFn: userService.getMe,
    staleTime: 1000, // prevent call twice when sign-in
    retry: 0,
    ...options
  })
}
