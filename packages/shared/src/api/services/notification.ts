/**
 * Notification API service
 * Services for managing notifications
 */

import type { NoticeCheckingResponse } from '@/libs/types'
import type { NoticeCheckingRequest } from '@/libs/types/api/notification'
import { API_ENDPOINTS, request } from '../core'

/**
 * Notification API service
 */
export const notificationService = {
  /**
   * Check for new notifications
   */
  getUnreadNotification: async (params: NoticeCheckingRequest) => {
    const res = await request<NoticeCheckingResponse>(
      API_ENDPOINTS.NOTICE.CHECK.buildUrl(),
      { params }
    )
    return res.data
  }
}
