/**
 * Authentication API service
 * Services for managing user authentication
 */

import { API_ENDPOINTS, request, type ServerResponse } from '../core'

/**
 * Authentication API service
 */
export const authService = {
  /**
   * Check login count for a specific email
   */
  checkLoginCount: async (email: string) => {
    const response = await request.post(
      API_ENDPOINTS.AUTH.CHECK_LOGIN.buildUrl(),
      { email }
    )
    return response.data
  },

  /**
   * Update login fail attempt for a specific email
   */
  updateLoginFail: async (email: string) => {
    const res = await request.patch<ServerResponse>(
      API_ENDPOINTS.AUTH.LOGIN_FAILED.buildUrl(),
      { email }
    )
    return res.data
  },

  /**
   * Reset login count after successful login
   */
  resetLoginCount: async () => {
    const res = await request.patch<ServerResponse>(
      API_ENDPOINTS.AUTH.LOGIN_SUCCESS.buildUrl()
    )
    return res.data
  }
}
