/**
 * Solicitor API service
 * Services for managing solicitor data and options
 */

import type {
  GetSolicitorCompanyDepartmentParamType,
  GetSolicitorCompanyResponse,
  GetSolicitorCompanyUserParamType,
  GetSolicitorDepartmentsResponse,
  GetSolicitorUsersResponse
} from '@/libs/types'
import { API_ENDPOINTS, request } from '../core'

/**
 * Type definition for solicitor
 */
export interface SolicitorCompanyType {
  company_name: string
  company_avatar_url: string
  company_id: number
  // user_id: number
  // user_name: string
  // user_avatar_url: string
}

export interface SolicitorUserType {
  company_name: string
  company_avatar_url: string
  company_id: number
  user_id: number
  user_name: string
  user_avatar_url: string
}

export interface SolicitorDepartmentType {
  id: number
  name: string
  company_id: number
}

/**
 * Solicitor API service
 */
export const solicitorService = {
  /**
   * Get company solicitor
   */
  getSolicitorCompany: async () => {
    const res = await request<GetSolicitorCompanyResponse>(
      API_ENDPOINTS.SOLICITOR.COMPANY.buildUrl()
    )
    return res.data
  },

  getSolicitorCompanyDepartment: async (
    params: GetSolicitorCompanyDepartmentParamType
  ) => {
    const res = await request<GetSolicitorDepartmentsResponse>(
      API_ENDPOINTS.SOLICITOR.DEPARTMENT.buildUrl(),
      { params }
    )
    return res.data
  },

  /**
   * Get solicitor users
   */
  getSolicitorUsers: async (params: GetSolicitorCompanyUserParamType) => {
    const res = await request<GetSolicitorUsersResponse>(
      API_ENDPOINTS.SOLICITOR.USERS.buildUrl(),
      { params }
    )

    return res.data
  }
}
