/**
 * Direct Message API service
 * Services for managing direct messages between users
 */

import type {
  CreateDmRequest,
  DmConversationDetailResponse,
  DmConversationListResponse,
  DmMessage,
  DmMessageListResponse,
  GetDmConversationListRequest,
  GetDmMessagesRequest,
  GetDmUnreadMessageResponse,
  SendDmMessageRequest,
  UpdateDmUnreadCountRequest
} from '@/libs/types'
import { extractUrlParams } from '@/libs/utils/helper'
import { API_ENDPOINTS, request, type ServerResponse } from '../core'

/**
 * DM API service
 */
export const dmService = {
  /**
   * Get list of DM conversations
   */
  getListConversations: async (params: GetDmConversationListRequest) => {
    const res = await request<DmConversationListResponse>(
      API_ENDPOINTS.DM.CONVERSATIONS.LIST.buildUrl(),
      { params }
    )
    return res.data
  },

  /**
   * Create DM with seller
   */
  createDMWithSeller: async (data: CreateDmRequest) => {
    const res = await request.post<ServerResponse<DmMessage>>(
      API_ENDPOINTS.DM.CONVERSATIONS.CREATE_SELLER.buildUrl(),
      data
    )
    return res.data
  },

  /**
   * Create DM with solicitor
   */
  createDMWithSolicitor: async (data: CreateDmRequest) => {
    const res = await request.post<ServerResponse<DmMessage>>(
      API_ENDPOINTS.DM.CONVERSATIONS.CREATE_SOLICITOR.buildUrl(),
      data
    )
    return res.data
  },

  /**
   * Get DM conversation detail
   */
  getConversationDetail: async (conversationId: string | number) => {
    const res = await request<DmConversationDetailResponse>(
      API_ENDPOINTS.DM.CONVERSATIONS.DETAIL.buildUrl({ conversationId })
    )
    return res
  },

  /**
   * Get DM conversation messages
   */
  getMessages: async ({
    conversationId,
    nextPageUrl
  }: GetDmMessagesRequest) => {
    const params = extractUrlParams(nextPageUrl ?? '')
    const endpoint = API_ENDPOINTS.DM.MESSAGES.LIST.buildUrl({ conversationId })

    const res = await request<DmMessageListResponse>(endpoint, { params })
    return res.data
  },

  /**
   * Send message in DM conversation
   */
  sendMessage: async ({ conversationId, content }: SendDmMessageRequest) => {
    const res = await request.post<ServerResponse<{ id: number }>>(
      API_ENDPOINTS.DM.MESSAGES.SEND.buildUrl({ conversationId }),
      { content }
    )
    return res.data
  },

  /**
   * Update unread message count for DM
   */
  updateUnreadCount: async ({ conversationId }: UpdateDmUnreadCountRequest) => {
    const res = await request.patch<ServerResponse>(
      API_ENDPOINTS.DM.MESSAGES.UPDATE_UNREAD.buildUrl({ conversationId })
    )
    return res.data
  },

  /**
   * Get unread messages for DM
   */
  getUnreadMessages: async (conversationId: string) => {
    const res = await request.get<GetDmUnreadMessageResponse>(
      API_ENDPOINTS.DM.MESSAGES.UNREAD.buildUrl({ conversationId })
    )
    return res
  }
}
