/**
 * Customer API service
 * Services for managing seller companies
 */

import type {
  AdminSellerCompanyListResponse,
  AdminSellerCompanyOptionResponse,
  AdminSellerDepartmentListResponse,
  CreateSellerCompanyRequest,
  CreateSellerCompanyResponse,
  DetailSellerCompanyResponse,
  GetAdminSellerCompanyRequest,
  GetAdminSellerDepartmentRequest
} from '@/libs/types/api/admin/seller-company'
import { request } from '../../core'
import { ADMIN_ENDPOINTS } from '../../core/admin-endpoint'

export const adminSellerCompanyService = {
  /**
   * Get list of customers - New API
   */
  getListSellerCompanies: async (params: GetAdminSellerCompanyRequest) => {
    const res = await request<AdminSellerCompanyListResponse>(
      ADMIN_ENDPOINTS.ADMIN_SELLER_COMPANY.LIST.buildUrl(),
      { params }
    )
    return res.data
  },

  getSellerCompanyOptions: async (params: GetAdminSellerCompanyRequest) => {
    const res = await request<AdminSellerCompanyOptionResponse>(
      ADMIN_ENDPOINTS.ADMIN_SELLER_COMPANY.LIST.buildUrl(),
      { params }
    )
    return res.data
  },

  createSellerCompany: async (data: CreateSellerCompanyRequest) => {
    const res = await request.post<CreateSellerCompanyResponse>(
      ADMIN_ENDPOINTS.ADMIN_SELLER_COMPANY.CREATE.buildUrl(),

      data
    )
    return res.data
  },

  updateSellerCompany: async (id: string, data: CreateSellerCompanyRequest) => {
    const res = await request.put<CreateSellerCompanyResponse>(
      ADMIN_ENDPOINTS.ADMIN_SELLER_COMPANY.UPDATE.buildUrl({ companyId: id }),

      data
    )
    return res.data
  },

  getDetailSellerCompany: async (id: string) => {
    const res = await request.get<DetailSellerCompanyResponse>(
      ADMIN_ENDPOINTS.ADMIN_SELLER_COMPANY.DETAIL.buildUrl({ companyId: id })
    )
    return res.data
  },

  getListDepartment: async (params: GetAdminSellerDepartmentRequest) => {
    const res = await request<AdminSellerDepartmentListResponse>(
      ADMIN_ENDPOINTS.ADMIN_SELLER_COMPANY.DEPARTMENT_LIST.buildUrl(),
      { params }
    )
    return res.data
  },

  getListSolicitorCompany: async (params: GetAdminSellerCompanyRequest) => {
    const res = await request<AdminSellerCompanyListResponse>(
      ADMIN_ENDPOINTS.ADMIN_SELLER_COMPANY.SOLICITOR_LIST.buildUrl(),
      { params }
    )
    return res.data
  },

  deleteRelationSolicitorCompany: async (relationId: string) => {
    const res = await request.delete(
      ADMIN_ENDPOINTS.ADMIN_SELLER_COMPANY.DELETE_RELATION.buildUrl({
        relationId
      })
    )
    return res.data
  },

  deleteSellerCompanyDepartment: async (departmentId: string) => {
    const res = await request.delete(
      ADMIN_ENDPOINTS.ADMIN_SELLER_COMPANY.DELETE_DEPARTMENT.buildUrl({
        departmentId
      })
    )
    return res.data
  },

  deleteSellerCompany: async (companyId: string | number) => {
    const res = await request.delete(
      ADMIN_ENDPOINTS.ADMIN_SELLER_COMPANY.DELETE.buildUrl({ companyId })
    )
    return res.data
  }
}
