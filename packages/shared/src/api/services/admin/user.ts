/**
 * Customer API service
 * Services for managing customers
 */

import type {
  AdminUserListResponse,
  CreateSellerUserRequest,
  CreateSolicitorUserRequest,
  GetAdminUserRequest,
  UpdateSellerUserRequest,
  UpdateSolicitorUserRequest
} from '@/libs/types/api/admin/admin-user'
import { request } from '../../core'
import { ADMIN_ENDPOINTS } from '../../core/admin-endpoint'

export const adminUserService = {
  /**
   * Get list of customers - New API
   */
  getListUsers: async (params: GetAdminUserRequest) => {
    const res = await request<AdminUserListResponse>(
      ADMIN_ENDPOINTS.ADMIN_USER.LIST.buildUrl(),
      { params }
    )
    return res.data
  },

  createSellerUser: async (data: CreateSellerUserRequest) => {
    const res = await request.post(
      ADMIN_ENDPOINTS.ADMIN_USER.CREATE_SELLER_USER.buildUrl(),
      data
    )
    return res.data
  },

  updateSellerUser: async (sellerId: string, data: UpdateSellerUserRequest) => {
    const res = await request.put(
      ADMIN_ENDPOINTS.ADMIN_USER.UPDATE_SELLER_USER.buildUrl({ sellerId }),
      data
    )
    return res.data
  },

  createSolicitorUser: async (data: CreateSolicitorUserRequest) => {
    const res = await request.post(
      ADMIN_ENDPOINTS.ADMIN_USER.CREATE_SOLICITOR_USER.buildUrl(),
      data
    )
    return res.data
  },

  updateSolicitorUser: async (
    solicitorId: string,
    data: UpdateSolicitorUserRequest
  ) => {
    const res = await request.put(
      ADMIN_ENDPOINTS.ADMIN_USER.UPDATE_SOLICITOR_USER.buildUrl({
        solicitorId
      }),
      data
    )
    return res.data
  },

  deleteUser: async (userId: string) => {
    const res = await request.delete(
      ADMIN_ENDPOINTS.ADMIN_USER.DELETE_USER.buildUrl({ userId })
    )
    return res.data
  }
}
