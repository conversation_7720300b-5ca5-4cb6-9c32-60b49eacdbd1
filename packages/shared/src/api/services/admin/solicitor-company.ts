/**
 * Customer API service
 * Services for managing solcitor company
 */

import type {
  AdminSolicitorCompanyListResponse,
  AdminSolicitorCompanyOptionResponse,
  AdminSolicitorDepartmentListResponse,
  CreateSolicitorCompanyRequest,
  CreateSolicitorCompanyResponse,
  DetailSolicitorCompanyResponse,
  GetAdminSolicitorCompanyRequest,
  GetAdminSolicitorDepartmentRequest
} from '@/libs/types/api/admin/solicitor-company'
import { request } from '../../core'
import { ADMIN_ENDPOINTS } from '../../core/admin-endpoint'

export const adminSolicitorCompanyService = {
  /**
   * Get list of solcitor company - New API
   */
  getListSolicitorCompany: async (params: GetAdminSolicitorCompanyRequest) => {
    const res = await request<AdminSolicitorCompanyListResponse>(
      ADMIN_ENDPOINTS.ADMIN_SOLICITOR_COMPANY.LIST.buildUrl(),
      { params }
    )
    return res.data
  },

  getSolicitorCompanyOptions: async (
    params: GetAdminSolicitorCompanyRequest
  ) => {
    const res = await request<AdminSolicitorCompanyOptionResponse>(
      ADMIN_ENDPOINTS.ADMIN_SOLICITOR_COMPANY.LIST.buildUrl(),
      { params }
    )
    return res.data
  },

  getListDepartment: async (params: GetAdminSolicitorDepartmentRequest) => {
    const res = await request<AdminSolicitorDepartmentListResponse>(
      ADMIN_ENDPOINTS.ADMIN_SOLICITOR_COMPANY.DEPARTMENT_LIST.buildUrl(),
      { params }
    )
    return res.data
  },

  /**
   * Create new solicitor company
   */
  createSolicitorCompany: async (data: CreateSolicitorCompanyRequest) => {
    const res = await request.post<CreateSolicitorCompanyResponse>(
      ADMIN_ENDPOINTS.ADMIN_SOLICITOR_COMPANY.CREATE.buildUrl(),
      data
    )
    return res.data
  },

  /**
   * get detail solicitor company
   */
  getDetailSolicitorCompany: async (companyId: string) => {
    const res = await request.get<DetailSolicitorCompanyResponse>(
      ADMIN_ENDPOINTS.ADMIN_SOLICITOR_COMPANY.DETAIL.buildUrl({ companyId })
    )
    return res.data
  },

  /**
   * Update existing solicitor company
   */
  updateSolicitorCompany: async (
    companyId: string | number,
    data: CreateSolicitorCompanyRequest
  ) => {
    const res = await request.put<CreateSolicitorCompanyResponse>(
      ADMIN_ENDPOINTS.ADMIN_SOLICITOR_COMPANY.UPDATE.buildUrl({ companyId }),
      data
    )
    return res.data
  },
  /**
   * Get list of seller company linked to solicitor company
   */
  getListSellerCompany: async (params: GetAdminSolicitorCompanyRequest) => {
    const res = await request<AdminSolicitorCompanyListResponse>(
      ADMIN_ENDPOINTS.ADMIN_SOLICITOR_COMPANY.SELLER_LIST.buildUrl(),
      { params }
    )
    return res.data
  },
  /**
   * Delete relation seller company
   */
  deleteRelationSellerCompany: async (relationId: string) => {
    const res = await request.delete(
      ADMIN_ENDPOINTS.ADMIN_SOLICITOR_COMPANY.DELETE_RELATION.buildUrl({
        relationId
      })
    )
    return res.data
  },

  deleteSolicitorCompanyDepartment: async (departmentId: string) => {
    const res = await request.delete(
      ADMIN_ENDPOINTS.ADMIN_SOLICITOR_COMPANY.DELETE_DEPARTMENT.buildUrl({
        departmentId
      })
    )
    return res.data
  },

  deleteSolicitorCompany: async (companyId: string | number) => {
    const res = await request.delete(
      ADMIN_ENDPOINTS.ADMIN_SOLICITOR_COMPANY.DELETE.buildUrl({ companyId })
    )
    return res.data
  }
}
