import { defineEndpoint } from './endpoint-factory'

export const AdminUserEndpoint = {
  LIST: defineEndpoint({
    path: '/admin/users',
    queryKey: 'admin-users-list',
    method: 'GET'
  }),
  CREATE_SELLER_USER: defineEndpoint({
    path: '/admin/users/seller',
    queryKey: 'admin-create-seller_user',
    method: 'POST'
  }),
  UPDATE_SELLER_USER: defineEndpoint({
    path: '/admin/users/seller/:sellerId',
    queryKey: 'admin-update-seller-user',
    method: 'PUT'
  }),
  CREATE_SOLICITOR_USER: defineEndpoint({
    path: '/admin/users/solicitor',
    queryKey: 'admin-create-solicitor-user',
    method: 'POST'
  }),
  UPDATE_SOLICITOR_USER: defineEndpoint({
    path: '/admin/users/solicitor/:solicitorId',
    queryKey: 'admin-update-solicitor-user',
    method: 'PUT'
  }),
  DELETE_USER: defineEndpoint({
    path: '/admin/users/:userId',
    queryKey: 'admin-delete-user',
    method: 'DELETE'
  })
}

export const AdminSellerCompanyEndpoint = {
  LIST: defineEndpoint({
    path: '/admin/company/sellers',
    queryKey: 'admin-seller-company-list',
    method: 'GET'
  }),
  CREATE: defineEndpoint({
    path: '/admin/company/sellers',
    queryKey: 'admin-seller-company-create',
    method: 'POST'
  }),
  UPDATE: defineEndpoint({
    path: '/admin/company/sellers/:companyId',
    queryKey: 'admin-seller-company-update',
    method: 'PUT'
  }),
  DELETE: defineEndpoint({
    path: '/admin/company/sellers/:companyId',
    queryKey: 'admin-seller-company-delete',
    method: 'DELETE'
  }),
  DETAIL: defineEndpoint({
    path: '/admin/company/sellers/:companyId',
    queryKey: 'admin-seller-company-detail',
    method: 'GET'
  }),
  DEPARTMENT_LIST: defineEndpoint({
    path: '/admin/company/sellers/department',
    queryKey: 'admin-seller-department-list',
    method: 'GET'
  }),
  SOLICITOR_LIST: defineEndpoint({
    path: '/admin/company/solicitors',
    queryKey: 'admin-seller-solicitor-list',
    method: 'GET'
  }),
  DELETE_RELATION: defineEndpoint({
    path: '/admin/companies/relations/:relationId',
    queryKey: 'admin-seller-company-delete-relation',
    method: 'DELETE'
  }),
  DELETE_DEPARTMENT: defineEndpoint({
    path: '/admin/company/sellers/department/:departmentId',
    queryKey: 'admin-sellers-company-department-delete',
    method: 'DELETE'
  })
}

export const OptionsEndpoints = {
  COMPANY_OPTION: defineEndpoint({
    path: 'dummy',
    queryKey: 'add-users-company-options'
  }),
  DEPARTMENT_OPTION: defineEndpoint({
    path: 'dummy',
    queryKey: 'add-users-department-options'
  })
}

export const AdminSolicitorCompanyEndpoint = {
  LIST: defineEndpoint({
    path: '/admin/company/solicitors',
    queryKey: 'admin-solicitors-company-list',
    method: 'GET'
  }),

  DETAIL: defineEndpoint({
    path: '/admin/company/solicitors/:companyId',
    queryKey: 'admin-solicitors-company-detail',
    method: 'GET'
  }),

  CREATE: defineEndpoint({
    path: '/admin/company/solicitors',
    queryKey: 'admin-solicitors-company-create',
    method: 'POST'
  }),

  UPDATE: defineEndpoint({
    path: '/admin/company/solicitors/:companyId',
    queryKey: 'admin-solicitors-company-update',
    method: 'PUT'
  }),

  DELETE: defineEndpoint({
    path: '/admin/company/solicitors/:companyId',
    queryKey: 'admin-solicitors-company-update',
    method: 'DELETE'
  }),
  DEPARTMENT_LIST: defineEndpoint({
    path: '/admin/company/solicitors/department',
    queryKey: 'admin-solicitor-department-list',
    method: 'GET'
  }),
  SELLER_LIST: defineEndpoint({
    path: '/admin/company/sellers',
    queryKey: 'admin-solicitor-seller-list',
    method: 'GET'
  }),
  DELETE_RELATION: defineEndpoint({
    path: '/admin/companies/relations/:relationId',
    queryKey: 'admin-solicitor-company-delete-relation',
    method: 'DELETE'
  }),
  DELETE_DEPARTMENT: defineEndpoint({
    path: '/admin/company/solicitors/department/:departmentId',
    queryKey: 'admin-solicitors-company-department-delete',
    method: 'DELETE'
  })
}

export const ADMIN_ENDPOINTS = {
  ADMIN_USER: AdminUserEndpoint,
  ADMIN_SELLER_COMPANY: AdminSellerCompanyEndpoint,
  ADMIN_SOLICITOR_COMPANY: AdminSolicitorCompanyEndpoint,
  OPTIONS: OptionsEndpoints
}
