import { fetchAuthSession, signOut } from 'aws-amplify/auth'
import axios, {
  type AxiosError,
  type AxiosResponse,
  type InternalAxiosRequestConfig
} from 'axios'
import { getDefaultStore } from 'jotai'
import { toast } from 'sonner'
import { accessToken<PERSON>tom, authAtom } from '../../auth/atom'
import type { ServerResponse } from './types'

/**
 * Create axios instance with default configuration
 */
const request = axios.create({
  baseURL: import.meta.env.VITE_API_URL,
  timeout: 30000,
  withCredentials: true
})

/**
 * Get access token from store or session
 * @returns access token or null if not found
 */
const getToken = async () => {
  const store = getDefaultStore()

  // Prioritize getting token from store to avoid unnecessary API calls
  const tokenFromStore = store.get(accessTokenAtom)
  if (tokenFromStore) return tokenFromStore

  // If not in store, get from session
  const session = await fetchAuthSession()
  const token = session.tokens?.accessToken.toString()
  if (!token) return null

  // Save token to store for future use
  store.set(accessTokenAtom, token)
  return token
}

/**
 * Interface for Promises in the queue during token refresh
 */
interface QueueItem {
  resolve: (token: string) => void
  reject: (error: Error) => void
}

// Token refresh state
let isRefreshing = false
let pendingRequests: QueueItem[] = []

/**
 * Process pending requests after token refresh
 * @param error Error if token refresh failed
 * @param token New token if refresh succeeded
 */
const processPendingRequests = (
  error: Error | null,
  token: string | null = null
) => {
  pendingRequests.forEach((request) => {
    if (error) {
      request.reject(error)
    } else if (token) {
      request.resolve(token)
    }
  })

  // Clear all pending requests after processing
  pendingRequests = []
}

/**
 * Forces user logout by clearing authentication state and signing out
 *
 * This function:
 * 1. Clears the authentication state in the store
 * 2. Signs out the user from the current session
 *
 * @returns {Promise<void>} A promise that resolves when logout is complete
 */
const forceLogout = async (store: ReturnType<typeof getDefaultStore>) => {
  store.set(authAtom, { isAuthenticated: false })
  await signOut()
}

/**
 * Register a request to wait for a new token
 * This function creates a Promise that will be resolved when a new token is available
 * or rejected if token refresh fails
 * @returns Promise that resolves with the new token
 */
const waitForNewToken = (): Promise<string> => {
  return new Promise<string>((resolve, reject) => {
    // Add callbacks to the pending queue
    // These will be called by processPendingRequests when token refresh completes
    pendingRequests.push({
      // Will be called with the new token when refresh succeeds
      resolve: (token: string) => resolve(token),
      // Will be called with the error when refresh fails
      reject: (error: Error) => reject(error)
    })
  })
}

/**
 * Retry a failed request with a new token
 * @param request The original request configuration
 * @param token The new access token
 * @returns A Promise with the retry request
 */
const retryRequestWithToken = (
  originalConfig: CustomInternalAxiosRequestConfig,
  token: string
): Promise<AxiosResponse> => {
  if (originalConfig.headers) {
    originalConfig.headers.Authorization = `Bearer ${token}`
  }
  return request(originalConfig)
}

/**
 * Interceptor to add token to header of each request
 */
request.interceptors.request.use(async (config) => {
  const token = await getToken()
  if (token) {
    config.headers.Authorization = `Bearer ${token}`
  }
  return config
})

/**
 * Extend axios config to add _retry field
 */
interface CustomInternalAxiosRequestConfig extends InternalAxiosRequestConfig {
  _retry?: boolean
}

/**
 * Response interceptor, including token refresh handling when receiving 401 error
 */
request.interceptors.response.use(
  // Return normal response if no error
  (response) => response,
  // Error handling
  async (error: AxiosError<ServerResponse>) => {
    // MARK: Check timeout request error
    if (error.code === 'ECONNABORTED' || error.message.includes('timeout')) {
      toast.error('データの読み込みに失敗しました。もう一度お試しください。', {
        position: 'top-center'
      })
      return Promise.reject(error)
    }

    const originalRequest = error.config as
      | CustomInternalAxiosRequestConfig
      | undefined

    const store = getDefaultStore()

    // Check if 401 (Unauthorized) error and request can be retried
    const isUnauthorizedError = error.response?.status === 401
    const canRetryRequest = originalRequest && !originalRequest._retry

    // MARK: force logout after a period of inactivity
    if (
      isUnauthorizedError &&
      error.response?.data.code === 'EXPIRED_SESSION'
    ) {
      await forceLogout(store)
      return Promise.reject(error)
    }

    if (isUnauthorizedError && canRetryRequest) {
      // If already refreshing token (another request is handling it)
      if (isRefreshing) {
        try {
          // Wait for the active refresh process to complete
          // This Promise will be resolved when processPendingRequests is called
          const newToken = await waitForNewToken()

          // When we get here, the token has been refreshed by another request
          // Now we can retry our original request with the new token
          return retryRequestWithToken(originalRequest, newToken)
        } catch (err) {
          // Token refresh failed, propagate the error
          return Promise.reject(err)
        }
      }

      // Mark this request as retried and start refresh process
      originalRequest._retry = true
      isRefreshing = true

      try {
        // Perform token refresh
        const session = await fetchAuthSession({ forceRefresh: true })
        const newToken = session.tokens?.accessToken.toString()

        if (!newToken) {
          throw new Error('Failed to refresh token')
        }

        // Save new token and update current request
        store.set(accessTokenAtom, newToken)

        // Process all pending requests with new token
        // This will resolve all the waitForNewToken() promises with the new token
        processPendingRequests(null, newToken)

        // Retry original request with new token
        return retryRequestWithToken(originalRequest, newToken)
      } catch (refreshError) {
        // Convert error if needed
        const error =
          refreshError instanceof Error
            ? refreshError
            : new Error('Unknown error when refreshing token')

        // Notify all pending requests of the error
        // This will reject all the waitForNewToken() promises with the error
        processPendingRequests(error, null)

        // Sign out user when token refresh fails
        await forceLogout(store)

        return Promise.reject(refreshError)
      } finally {
        // Mark refresh process as completed
        isRefreshing = false
      }
    }

    // Return original error if not 401 or can't retry
    return Promise.reject(error)
  }
)

export default request
