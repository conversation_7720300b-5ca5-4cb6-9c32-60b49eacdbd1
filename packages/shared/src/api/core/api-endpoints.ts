/**
 * API endpoint definitions using factory pattern
 *
 */

import { defineEndpoint, REFRESH_INTERVALS } from './endpoint-factory'
/**
 * Customer domain API endpoints
 * MARK: Auth
 * Usage examples:
 */
export const AuthEndpoints = {
  CHECK_LOGIN: defineEndpoint({
    path: '/check-login',
    queryKey: 'auth-check-login',
    method: 'GET'
  }),
  LOGIN_FAILED: defineEndpoint({
    path: '/login-fail',
    queryKey: 'auth-login-fail',
    method: 'PATCH'
  }),
  LOGIN_SUCCESS: defineEndpoint({
    path: '/login-success',
    queryKey: 'auth-login-success',
    method: 'PATCH'
  }),
  UNLOCK_ACCOUNT: defineEndpoint({
    path: '/admin/users/:userId',
    queryKey: 'auth-unlock-account',
    method: 'PATCH'
  })
}

/**
 * Customer domain API endpoints
 * MARK: Customer
 *
 * Usage examples:
 * - CustomerEndpoints.LIST.buildUrl() // No params needed
 * - CustomerEndpoints.DETAIL.buildUrl({ customerId: 123 }) // Requires customerId
 */
export const CustomerEndpoints = {
  LIST: defineEndpoint({
    path: '/customers',
    queryKey: 'customers-list',
    refetchInterval: REFRESH_INTERVALS.MEDIUM,
    method: 'GET',
    staleTime: REFRESH_INTERVALS.SHORT
  }),

  DETAIL: defineEndpoint({
    path: '/customers/:customerId',
    queryKey: 'customer-detail',
    method: 'GET'
  }),

  CREATE: defineEndpoint({
    path: '/customers',
    queryKey: 'customer-create',
    method: 'POST'
  }),

  UPDATE: defineEndpoint({
    path: '/customers/:customerId',
    queryKey: 'customer-update',
    method: 'PUT'
  })
}

/**
 * Conversation domain API endpoints
 * MARK: Conversation
 */
export const GRConversationEndpoints = {
  LIST_SELLER: defineEndpoint({
    path: '/conversations/:customerId',
    queryKey: 'seller-conversations-list',
    refetchInterval: REFRESH_INTERVALS.MEDIUM
  }),

  LIST_SOLICITOR: defineEndpoint({
    path: '/conversations',
    queryKey: 'solicitor-conversations-list',
    refetchInterval: REFRESH_INTERVALS.MEDIUM
  }),

  ADD: defineEndpoint({
    path: '/conversations/:customerId/add-conversation',
    queryKey: 'conversation-add',
    method: 'POST'
  }),

  DETAIL: defineEndpoint({
    path: '/conversations/:conversationId/companies',
    queryKey: 'conversation-detail',
    refetchInterval: REFRESH_INTERVALS.MEDIUM
  }),

  COMPANIES: defineEndpoint({
    path: '/conversations/:conversationId/companies',
    queryKey: 'conversation-companies',
    refetchInterval: REFRESH_INTERVALS.MEDIUM
  }),

  UPDATE_SOLICITOR_CONVERSATION_INFO: defineEndpoint({
    path: '/conversations/:conversationId/history',
    queryKey: 'update-conversation-history',
    method: 'PUT'
  }),

  MESSAGES: {
    LIST: defineEndpoint({
      path: '/conversations/:conversationId/messages',
      queryKey: 'conversation-messages'
    }),

    SEND: defineEndpoint({
      path: '/conversations/:conversationId/messages',
      queryKey: 'conversation-send-message',
      method: 'POST'
    }),

    UPDATE_UNREAD: defineEndpoint({
      path: '/conversations/:conversationId/messages/unread',
      queryKey: 'update-conversation-unread',
      method: 'PATCH'
    }),

    UNREAD: defineEndpoint({
      path: '/conversations/:conversationId/messages/unread',
      queryKey: 'conversation-unread',
      refetchInterval: REFRESH_INTERVALS.MEDIUM
    })
  }
}

/**
 * Direct Message domain API endpoints
 * MARK: Direct Message
 */
export const DMEndpoints = {
  CONVERSATIONS: {
    LIST: defineEndpoint({
      path: '/dm/conversations',
      queryKey: 'dm-conversations-list',
      refetchInterval: REFRESH_INTERVALS.MEDIUM
    }),

    CREATE_SELLER: defineEndpoint({
      path: '/dm/conversations/seller',
      queryKey: 'dm-create-seller',
      method: 'POST'
    }),

    CREATE_SOLICITOR: defineEndpoint({
      path: '/dm/conversations/solicitor',
      queryKey: 'dm-create-solicitor',
      method: 'POST'
    }),

    DETAIL: defineEndpoint({
      path: '/dm/:conversationId',
      queryKey: 'dm-detail',
      refetchInterval: REFRESH_INTERVALS.MEDIUM
    })
  },

  MESSAGES: {
    LIST: defineEndpoint({
      path: '/dm/:conversationId/messages',
      queryKey: 'dm-messages'
    }),

    SEND: defineEndpoint({
      path: '/dm/:conversationId/messages',
      queryKey: 'dm-send-message',
      method: 'POST'
    }),

    UPDATE_UNREAD: defineEndpoint({
      path: '/dm/:conversationId/messages/unread',
      queryKey: 'dm-update-unread',
      method: 'PATCH'
    }),

    UNREAD: defineEndpoint({
      path: '/dm/:conversationId/messages/unread',
      queryKey: 'dm-unread',
      refetchInterval: REFRESH_INTERVALS.MEDIUM
    })
  }
}

/**
 * User domain API endpoints
 * MARK: User
 */
export const UserEndpoints = {
  ME: defineEndpoint({
    path: '/me',
    queryKey: 'user-me',
    staleTime: REFRESH_INTERVALS.LONG
  })
}

/**
 * Solicitor domain API endpoints
 * MARK: Solicitor
 */
export const SolicitorEndpoints = {
  COMPANY: defineEndpoint({
    path: '/company/solicitors',
    queryKey: 'solicitor-company'
  }),

  DEPARTMENT: defineEndpoint({
    path: '/company/solicitors/departments',
    queryKey: 'solicitor-departments'
  }),

  USERS: defineEndpoint({
    path: '/company/solicitors/users',
    queryKey: 'solicitor-users'
  })
}

/**
 * Seller domain API endpoints
 * MARK: Seller
 */
export const SellerEndpoints = {
  COMPANY: defineEndpoint({
    path: '/company/sellers',
    queryKey: 'seller-company'
  }),

  DEPARTMENT: defineEndpoint({
    path: '/company/sellers/departments',
    queryKey: 'seller-departments'
  }),

  USERS: defineEndpoint({
    path: '/company/sellers/users',
    queryKey: 'seller-users'
  })
}

/**
 * Options domain API endpoints
 * MARK: Options
 */
export const OptionsEndpoints = {
  SOLICITOR_USERS: defineEndpoint({
    path: '/company/solicitors/users',
    queryKey: 'solicitor-users'
  }),

  SELLER_USERS: defineEndpoint({
    path: '/company/sellers/users',
    queryKey: 'seller-users'
  }),

  CREATE_DM_USERS: defineEndpoint({
    path: 'dummy',
    queryKey: 'create-dm-users-options'
  })
}

/**
 * Notice API endpoints
 * MARK: Notice
 */
export const NoticeEndpoints = {
  CHECK: defineEndpoint({
    path: '/notice',
    queryKey: 'notice-check',
    refetchInterval: REFRESH_INTERVALS.MEDIUM
  })
}

/**
 * Export all endpoints by domain group
 */
export const API_ENDPOINTS = {
  AUTH: AuthEndpoints,
  CUSTOMER: CustomerEndpoints,
  CONVERSATION: GRConversationEndpoints,
  DM: DMEndpoints,
  USER: UserEndpoints,
  NOTICE: NoticeEndpoints,
  OPTIONS: OptionsEndpoints,
  SOLICITOR: SolicitorEndpoints,
  SELLER: SellerEndpoints
}
