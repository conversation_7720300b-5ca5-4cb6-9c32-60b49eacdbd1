/**
 * API endpoint factory
 * Provides consistent structure for all API endpoints
 */

// Refresh interval constants
export const REFRESH_INTERVALS = {
  NEVER: 0,
  SHORT: 10 * 1000, // 10 seconds
  NORMAL: 30 * 1000, // 30 seconds
  MEDIUM: 60 * 1000, // 1 minute
  LONG: 5 * 60 * 1000 // 5 minutes
}

export type EndpointParamProperty = string | number
export type EndpointParams = Record<string, EndpointParamProperty>

/**
 * Base endpoint definition interface
 */
export interface EndpointDefinition<
  TParams extends EndpointParams = EndpointParams
> {
  /**
   * Path pattern with optional placeholders
   * Example: "/customers/:customerId/conversations"
   */
  path: string

  /** React Query key */
  queryKey: string | string[]

  /**
   * Refetch interval for React Query in ms
   * @default 0 - No auto-refresh
   */
  refetchInterval?: number

  /**
   * HTTP method
   * @default 'GET'
   */
  method?: 'GET' | 'POST' | 'PUT' | 'PATCH' | 'DELETE'

  /**
   * Cache time for React Query in ms
   * @default 5 * 60 * 1000 (5 minutes)
   */
  cacheTime?: number

  /**
   * Stale time for React Query in ms
   * @default 0
   */
  staleTime?: number

  /**
   * Retry count for React Query
   * @default 3
   */
  retry?: number | boolean

  /**
   * Helper to build URL with params
   * @param params - Object containing required parameters
   * @returns Built URL
   */
  buildUrl: (params?: TParams) => string
}

// Path param extraction type utilities
type PathParam<T extends string> = T extends `:${infer P}` ? P : never

type ExtractParams<T extends string> =
  T extends `${string}:${infer Param}/${infer Rest}`
    ? PathParam<`:${Param}`> | ExtractParams<Rest>
    : T extends `${string}:${infer Param}`
      ? PathParam<`:${Param}`>
      : never

type ParamsFromPath<T extends string> = {
  [K in ExtractParams<T>]: EndpointParamProperty
}

type HasParams<T extends string> = ExtractParams<T> extends never ? false : true

// Type for endpoints with/without params
type EndpointWithParams<T extends EndpointParams> = Omit<
  EndpointDefinition<T>,
  'buildUrl'
> & {
  buildUrl: (params: T) => string
}

type EndpointWithoutParams = Omit<
  EndpointDefinition<Record<string, never>>,
  'buildUrl'
> & {
  buildUrl: () => string
}

// Final return type
export type EndpointResult<T extends string> = HasParams<T> extends true
  ? EndpointWithParams<ParamsFromPath<T>>
  : EndpointWithoutParams

/**
 * Creates an endpoint definition with correct typing based on path
 * Automatically infers parameter types from path
 *
 * @example
 * // No params required:
 * const listEndpoint = defineEndpoint({ path: '/customers' })
 * listEndpoint.buildUrl() // ✓ Valid
 *
 * // Params required:
 * const detailEndpoint = defineEndpoint({ path: '/customers/:id' })
 * detailEndpoint.buildUrl({ id: 123 }) // ✓ Valid
 * detailEndpoint.buildUrl() // ✗ Error: Missing required param
 */
export function defineEndpoint<TPath extends string>(config: {
  path: TPath
  queryKey: string | string[]
  method?: 'GET' | 'POST' | 'PUT' | 'PATCH' | 'DELETE'
  refetchInterval?: number
  cacheTime?: number
  staleTime?: number
  retry?: number | boolean
}): EndpointResult<TPath> {
  // Extract path parameters
  const extractPathParams = (path: string): string[] => {
    const paramRegex = /:([a-zA-Z0-9_]+)/g
    const matches = [...path.matchAll(paramRegex)]
    return matches.map((match) => match[1])
  }

  const pathParams = extractPathParams(config.path)
  const hasParams = pathParams.length > 0

  // Create buildUrl function based on path params
  const buildUrl = (params?: Record<string, EndpointParamProperty>): string => {
    let url = config.path as string

    if (hasParams) {
      if (!params) {
        throw new Error(`Missing required params: ${pathParams.join(', ')}`)
      }

      for (const param of pathParams) {
        const value = params[param]
        if (value === undefined) {
          throw new Error(`Missing required param: ${param}`)
        }
        url = url.replace(`:${param}`, String(value))
      }
    }

    return url
  }

  // Create endpoint with correct typing
  return {
    path: config.path,
    queryKey: config.queryKey,
    method: config.method || 'GET',
    refetchInterval: config.refetchInterval || 0,
    cacheTime: config.cacheTime || 5 * 60 * 1000,
    staleTime: config.staleTime || 0,
    retry: config.retry !== undefined ? config.retry : 3,
    buildUrl
  } as EndpointResult<TPath>
}
