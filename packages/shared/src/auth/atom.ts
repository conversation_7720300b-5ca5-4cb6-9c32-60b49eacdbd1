import { atom } from 'jotai'
import { atomWithStorage } from 'jotai/utils'

import { queryClient } from '@/react-query/client'
import type { User } from '@/types'
import { navigateAfterLogin, navigateAfterLogout } from '@/utils/navigation'

type SetAuthPayload = {
  isAuthenticated: boolean
  user?: User
}

export const userAtom = atomWithStorage<User | null>('user', null)
export const isAuthUserLoadingAtom = atom<boolean>(true)

export const isAuthenticationAtom = atom<boolean>(false)
export const accessTokenAtom = atom<string | null>(null)

export const authAtom = atom(
  (get) => ({
    isAuthenticated: get(isAuthenticationAtom),
    isAuthUserLoading: get(isAuthUserLoadingAtom),
    user: get(userAtom)
  }),
  async (_, set, auth: SetAuthPayload) => {
    auth.user && set(userAtom, auth.user)
    set(isAuthenticationAtom, auth.isAuthenticated)
    set(isAuthUserLoadingAtom, false)
    // clear user and token when logout
    if (!auth.isAuthenticated) {
      set(clearAuthAtom)
      navigateAfterLogout()
      return
    }

    navigateAfterLogin(auth.user!)
  }
)

export const clearAuthAtom = atom(null, async (_, set) => {
  set(userAtom, null)
  set(accessTokenAtom, null)
  queryClient.clear()
})
