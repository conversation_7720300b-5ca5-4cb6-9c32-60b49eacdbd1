import { UserRole } from '../constants'
import type { User } from '../types'

// User role checking helpers
export const isOwnerUser = (user: User) => user.role === UserRole.Owner

export const isContractorUser = (user: User) =>
  user.role === UserRole.Contractor

export const isAdmin = (user: User) => user.role === UserRole.Admin

// Role-based routing
export const getRoleDefaultPath = (user: User | null) => {
  if (!user) return '/login'
  if (isAdmin(user)) return '/admin'
  return isOwnerUser(user) ? '/owner' : '/contractor'
}

// Role-based access control for routes
export const checkRoleAccess = (
  user: User | null,
  allowedRoles: UserRole[]
) => {
  if (!user) return false
  return allowedRoles.includes(user.role)
}

// Route helper for redirecting based on role
export const roleBasedRedirect = (
  user: User | null,
  defaultPath = '/login'
) => {
  if (!user) return defaultPath
  return getRoleDefaultPath(user)
}
