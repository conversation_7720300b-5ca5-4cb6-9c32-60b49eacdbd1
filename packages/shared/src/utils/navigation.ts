import type {
  NavigateOptions,
  ParsedLocation,
  RegisteredRouter
} from '@tanstack/react-router'
import type { User } from '@/types'
import { AUTH_ROUTES } from '../constants'
import { getRoleDefaultPath } from './role'

export interface INavigationService {
  navigate: (options: NavigateOptions) => void
  getLocation: () => { pathname?: string }
  parseLocation: () => ParsedLocation
}

let navigationService: INavigationService | null = null

export const initNavigationService = (routerInstance: INavigationService) => {
  navigationService = routerInstance
}

type RouteToPath = NavigateOptions<RegisteredRouter>['to']

export const navigateAfterLogin = (user: User) => {
  if (!navigationService) {
    console.error('Navigation service is not initialized')
    return
  }

  const defaultPath = getRoleDefaultPath(user)
  navigationService.navigate({
    ...navigationService.parseLocation(),
    to: (navigationService.parseLocation().pathname ??
      defaultPath) as RouteToPath
  })
}

// Điều hướng sau khi đăng xuất
export const navigateAfterLogout = () => {
  if (!navigationService) {
    console.error('Navigation service is not initialized')
    return
  }

  navigationService.navigate({
    to: AUTH_ROUTES.LOGIN as RouteToPath,
    replace: true
  })
}

// Điều hướng theo pathname hoặc default path
export const navigateToPathOrDefault = (user: User | null, path?: string) => {
  if (!navigationService) {
    console.error('Navigation service is not initialized')
    return
  }

  if (!user) {
    navigationService.navigate({
      to: AUTH_ROUTES.LOGIN as RouteToPath,
      replace: true
    })
    return
  }

  const targetPath = path || getRoleDefaultPath(user)
  navigationService.navigate({
    to: targetPath as RouteToPath,
    replace: true
  })
}
