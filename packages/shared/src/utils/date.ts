import dayjs from './dayjs'

export const messageDateFormat = (date?: string | null) => {
  if (!date) return ''

  const inputDate = dayjs(date)
  if (!inputDate.isValid()) return ''

  const now = dayjs()
  const isSameDay = inputDate.format('YYYY-MM-DD') === now.format('YYYY-MM-DD')
  const isSameYear = inputDate.year() === now.year()

  if (isSameDay) return inputDate.format('HH:mm')
  if (isSameYear) return inputDate.format('MM/DD')
  return inputDate.format('YYYY/MM/DD')
}

export const timeInMessageFormat = (
  time: string | null,
  template = 'HH:mm'
) => {
  if (!time) return ''

  const inputDate = dayjs(time)
  if (!inputDate.isValid()) return ''

  return inputDate.format(template)
}

export const dateInMessageFormat = (time?: string | null) => {
  if (!time) return ''

  const date = dayjs(time)
  const today = dayjs()

  if (date.isSame(today, 'day')) {
    return ''
  }

  // Check if date is in current year
  if (date.year() === today.year()) {
    return date.format('MM/DD')
  }

  // If date is from different year
  return date.format('YYYY/MM/DD')
}

export const listMessageDateFormat = (dateString: string): string => {
  const date = dayjs(dateString)
  const today = dayjs()

  // Check if date is today
  if (date.format('YYYY-MM-DD') === today.format('YYYY-MM-DD')) {
    return '今日'
  }

  // Check if date is in current year
  if (date.year() === today.year()) {
    return date.format('MM月DD日')
  }

  // If date is from different year
  return date.format('YYYY年MM月DD日')
}

export const formatDateTemplate = (
  dateString: string | Date | null,
  template = 'YYYY/MM/DD'
) => {
  if (!dateString) return dateString
  const inputDate = dayjs(dateString)
  if (!inputDate.isValid()) return ''
  return inputDate.format(template)
}
