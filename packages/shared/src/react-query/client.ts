import { QueryClient } from '@tanstack/react-query'
import { AxiosError } from 'axios'
import { toast } from 'sonner'
import messages from '../messages/errors'

export const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: 1,
      refetchOnWindowFocus: false,
      throwOnError(error) {
        if (error instanceof AxiosError) {
          if (error.status === 403 || error.status === 503) {
            return true
          }
          if (error.status === 500) {
            toast.error(messages.INTERNAL_SERVER_ERROR, {
              position: 'top-center'
            })
          }
        }

        return false
      }
    }
  }
})
