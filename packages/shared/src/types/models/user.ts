import { z } from 'zod'
import { UserRole } from '../../constants'

// User schema
export const UserSchema = z.object({
  id: z.string(),
  email: z.string().email(),
  name: z.string(),
  role: z.number().refine((value) => Object.values(UserRole).includes(value), {
    message: 'Invalid role'
  }),
  avatar: z.string().optional(),
  createdAt: z.date(),
  updatedAt: z.date()
})

export type User = z.infer<typeof UserSchema>

// Authentication schemas
export const LoginSchema = z.object({
  email: z.string().email(),
  password: z.string().min(6)
})

export type LoginRequest = z.infer<typeof LoginSchema>

export const RegisterSchema = z.object({
  email: z.string().email(),
  password: z.string().min(6),
  name: z.string().min(2),
  role: z.number().refine((value) => Object.values(UserRole).includes(value), {
    message: 'Invalid role'
  })
})

export type RegisterRequest = z.infer<typeof RegisterSchema>

// Auth context type
export interface AuthContextType {
  user: User | null
  isLoading: boolean
  login: (credentials: LoginRequest, redirectTo?: string) => Promise<void>
  register: (data: RegisterRequest) => Promise<void>
  logout: () => Promise<void>
  hasRole: (role: UserRole) => boolean
  hasAnyRole: (roles: UserRole[]) => boolean
}
