// Application constants
export const APP_NAME = 'Novaest'
export const APP_DESCRIPTION = 'Modern role-based application platform'

// Local storage keys
export const STORAGE_KEYS = {
  AUTH_TOKEN: 'novaest_auth_token',
  REFRESH_TOKEN: 'novaest_refresh_token',
  USER_PREFERENCES: 'novaest_user_preferences',
  THEME: 'novaest_theme'
} as const

// Validation constants
export const VALIDATION = {
  PASSWORD_MIN_LENGTH: 6,
  NAME_MIN_LENGTH: 2,
  EMAIL_REGEX: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
  PHONE_REGEX: /^\+?[\d\s-()]+$/
} as const

export enum UserRole {
  Owner = 1,
  Contractor = 2,
  Admin = 3
}

export const ROLE_MAP = {
  [UserRole.Owner]: 'セラー',
  [UserRole.Contractor]: 'セラー管理者',
  [UserRole.Admin]: '弁護士'
}

export const REGEX_HALF_WIDTH = /^[\x20-\x7E]+$/
export const REGEX_HAS_ALPHANUMERIC = /^(?=.*[A-Za-z])(?=.*\d)/
export const REGEX_HAS_SPECIAL_CHAR = /(?=.*[^A-Za-z0-9])/

export const AUTH_ROUTES = {
  LOGIN: '/login',
  FORGOT_PASSWORD: '/forgot-password',
  RESET_PASSWORD: '/reset-password',
  FORCE_CHANGE_PASSWORD: '/force-change-password'
}

export const HAS_LOWERCASE = /[a-z]/
export const HAS_UPPERCASE = /[A-Z]/
export const HAS_NUMBER = /\d/
export const HAS_SPECIAL_CHAR = /[!@#$%^&*()_+\-=[\]{};':"\\|,.<>/?]/

export function checkThreeTypes(password: string): boolean {
  const checks = [
    HAS_LOWERCASE.test(password),
    HAS_UPPERCASE.test(password),
    HAS_NUMBER.test(password),
    HAS_SPECIAL_CHAR.test(password)
  ]
  return checks.filter(Boolean).length >= 3
}
