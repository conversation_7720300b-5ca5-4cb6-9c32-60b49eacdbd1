import { formatDateTime } from '@shared'
import {
  ActionIcon,
  Badge,
  Card,
  Container,
  Group,
  IconCheck,
  IconClock,
  Stack,
  Text,
  Title
} from '@ui'
import type { Task } from '../types'

interface TaskListProps {
  tasks: Task[]
  isLoading?: boolean
  onUpdateStatus?: (taskId: string, status: Task['status']) => void
  onLogTime?: (task: Task) => void
}

export function TaskList({
  tasks,
  isLoading = false,
  onUpdateStatus,
  onLogTime
}: TaskListProps) {
  const getStatusColor = (status: Task['status']) => {
    switch (status) {
      case 'todo':
        return 'gray'
      case 'in_progress':
        return 'blue'
      case 'review':
        return 'yellow'
      case 'completed':
        return 'green'
      default:
        return 'gray'
    }
  }

  const getPriorityColor = (priority: Task['priority']) => {
    switch (priority) {
      case 'low':
        return 'green'
      case 'medium':
        return 'yellow'
      case 'high':
        return 'orange'
      case 'urgent':
        return 'red'
      default:
        return 'gray'
    }
  }

  if (isLoading) {
    return (
      <Container size="xl" py="xl">
        <Stack gap="md">
          <Title order={2}>My Tasks</Title>
          {Array.from({ length: 3 }).map((_, index) => (
            <Card key={index} withBorder>
              <Text>Loading...</Text>
            </Card>
          ))}
        </Stack>
      </Container>
    )
  }

  if (tasks.length === 0) {
    return (
      <Container size="xl" py="xl">
        <Stack gap="md">
          <Title order={2}>My Tasks</Title>
          <Card withBorder>
            <Text c="dimmed" ta="center" py="xl">
              No tasks assigned yet.
            </Text>
          </Card>
        </Stack>
      </Container>
    )
  }

  return (
    <Container size="xl" py="xl">
      <Stack gap="md">
        <Title order={2}>My Tasks</Title>

        {tasks.map((task) => (
          <Card key={task.id} withBorder>
            <Group justify="space-between" align="flex-start">
              <Stack gap="xs" style={{ flex: 1 }}>
                <Group gap="sm">
                  <Title order={4}>{task.title}</Title>
                  <Badge color={getStatusColor(task.status)} variant="light">
                    {task.status.replace('_', ' ')}
                  </Badge>
                  <Badge
                    color={getPriorityColor(task.priority)}
                    variant="outline"
                    size="sm"
                  >
                    {task.priority}
                  </Badge>
                </Group>

                <Text c="dimmed" size="sm">
                  {task.description}
                </Text>

                <Group gap="md">
                  <Text size="sm">
                    <strong>Estimated:</strong> {task.estimatedHours}h
                  </Text>
                  {task.actualHours && (
                    <Text size="sm">
                      <strong>Actual:</strong> {task.actualHours}h
                    </Text>
                  )}
                  <Text size="sm">
                    <strong>Due:</strong> {formatDateTime(task.dueDate)}
                  </Text>
                </Group>
              </Stack>

              <Group gap="xs">
                {onLogTime && task.status !== 'completed' && (
                  <ActionIcon
                    variant="subtle"
                    color="blue"
                    onClick={() => onLogTime(task)}
                  >
                    <IconClock size={16} />
                  </ActionIcon>
                )}
                {onUpdateStatus && task.status !== 'completed' && (
                  <ActionIcon
                    variant="subtle"
                    color="green"
                    onClick={() => onUpdateStatus(task.id, 'completed')}
                  >
                    <IconCheck size={16} />
                  </ActionIcon>
                )}
              </Group>
            </Group>
          </Card>
        ))}
      </Stack>
    </Container>
  )
}
