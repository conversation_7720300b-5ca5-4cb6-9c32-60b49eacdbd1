# 🛠️ Development Guide

## Getting Started

### Prerequisites

- **Node.js** >= 18.0.0
- **pnpm** >= 8.0.0 (recommended package manager)
- **TypeScript** knowledge
- **React** experience

### Initial Setup

1. **Clone and Install**
```bash
git clone <repository-url>
cd novaest
pnpm install
```

2. **Environment Setup**
```bash
# Copy environment template (if exists)
cp .env.example .env.local

# Start development server
pnpm dev
```

3. **Verify Installation**
```bash
# Check dependency health
pnpm deps:check

# Run type checking
pnpm check-types

# Run linting
pnpm lint
```

## 🏗️ Development Workflow

### Working with Packages

#### 1. **Developing a Specific Package**
```bash
# Work on owner package
pnpm --filter @novaest/owner dev

# Build owner package
pnpm --filter @novaest/owner build

# Test owner package
pnpm --filter @novaest/owner test
```

#### 2. **Cross-Package Development**
```bash
# Build all packages first
pnpm build:packages

# Start main application
pnpm dev

# Or build everything
pnpm build
```

#### 3. **Adding New Components**

**To a Role Package (e.g., Owner):**
```bash
# Navigate to package
cd packages/owner/src/components

# Create new component
mkdir NewComponent
touch NewComponent/index.tsx
touch NewComponent/NewComponent.tsx
```

```typescript
// packages/owner/src/components/NewComponent/NewComponent.tsx
import React from 'react'
import { Card, Button } from '@ui'

interface NewComponentProps {
  title: string
  onAction: () => void
}

export const NewComponent: React.FC<NewComponentProps> = ({ 
  title, 
  onAction 
}) => {
  return (
    <Card>
      <h3>{title}</h3>
      <Button onClick={onAction}>
        Take Action
      </Button>
    </Card>
  )
}
```

```typescript
// packages/owner/src/components/NewComponent/index.tsx
export { NewComponent } from './NewComponent'
```

**To Shared UI Package:**
```bash
# Navigate to UI package
cd packages/ui/src/components

# Create component with Mantine integration
mkdir DataGrid
touch DataGrid/index.tsx
touch DataGrid/DataGrid.tsx
```

#### 4. **Adding New Utilities**

**To Shared Package:**
```typescript
// packages/shared/src/utils/dateUtils.ts
export const formatRelativeTime = (date: Date): string => {
  const now = new Date()
  const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000)
  
  if (diffInSeconds < 60) return 'just now'
  if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}m ago`
  if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)}h ago`
  
  return date.toLocaleDateString()
}
```

```typescript
// packages/shared/src/index.ts
export * from './utils/dateUtils'
```

### Working with Routes

#### 1. **Adding New Routes**
```bash
# Navigate to web app routes
cd apps/web/src/routes

# Create new route file
touch owner.projects.tsx
```

```typescript
// apps/web/src/routes/owner.projects.tsx
import { createFileRoute } from '@tanstack/react-router'
import { ProjectList } from '@owner'

export const Route = createFileRoute('/owner/projects')({
  beforeLoad: ({ context }) => {
    if (!context.auth.user || context.auth.user.role !== 'owner') {
      throw redirect({ to: '/login' })
    }
  },
  component: () => <ProjectList />
})
```

#### 2. **Route Protection Patterns**
```typescript
// Common auth guard
const requireAuth = (context: any) => {
  if (!context.auth.user) {
    throw redirect({ to: '/login' })
  }
}

// Role-specific guard
const requireRole = (role: string) => (context: any) => {
  requireAuth(context)
  if (context.auth.user.role !== role) {
    throw redirect({ to: '/unauthorized' })
  }
}

// Usage in route
export const Route = createFileRoute('/admin/users')({
  beforeLoad: requireRole('admin'),
  component: AdminUserManagement
})
```

## 🧪 Testing Guidelines

### 1. **Unit Testing Components**
```typescript
// packages/owner/src/components/__tests__/ProjectCard.test.tsx
import { render, screen } from '@testing-library/react'
import { ProjectCard } from '../ProjectCard'

describe('ProjectCard', () => {
  const mockProject = {
    id: '1',
    name: 'Test Project',
    status: 'active',
    progress: 75
  }

  it('renders project information correctly', () => {
    render(<ProjectCard project={mockProject} />)
    
    expect(screen.getByText('Test Project')).toBeInTheDocument()
    expect(screen.getByText('75%')).toBeInTheDocument()
  })

  it('handles click events', async () => {
    const onClickMock = jest.fn()
    render(<ProjectCard project={mockProject} onClick={onClickMock} />)
    
    await user.click(screen.getByRole('button'))
    expect(onClickMock).toHaveBeenCalledWith(mockProject)
  })
})
```

### 2. **Integration Testing**
```typescript
// apps/web/src/__tests__/auth-flow.test.tsx
import { renderWithProviders } from '../test-utils'
import { AuthProvider } from '../contexts/AuthContext'

describe('Authentication Flow', () => {
  it('redirects to correct dashboard after login', async () => {
    const { user } = renderWithProviders(<App />, {
      initialEntries: ['/login']
    })

    await user.type(screen.getByLabelText('Email'), '<EMAIL>')
    await user.type(screen.getByLabelText('Password'), 'password')
    await user.click(screen.getByRole('button', { name: 'Login' }))

    expect(screen.getByText('Owner Dashboard')).toBeInTheDocument()
  })
})
```

### 3. **Testing Utilities**
```typescript
// apps/web/src/test-utils.tsx
import React from 'react'
import { render } from '@testing-library/react'
import { MantineProvider } from '@mantine/core'
import { AuthProvider } from './contexts/AuthContext'

export const renderWithProviders = (
  ui: React.ReactElement,
  options?: {
    user?: User
    initialEntries?: string[]
  }
) => {
  const Wrapper = ({ children }: { children: React.ReactNode }) => (
    <MantineProvider>
      <AuthProvider initialUser={options?.user}>
        {children}
      </AuthProvider>
    </MantineProvider>
  )

  return render(ui, { wrapper: Wrapper, ...options })
}
```

## 🎨 Styling Guidelines

### 1. **Using Mantine Components**
```typescript
// Prefer Mantine components from @novaest/ui
import { Button, Card, Text } from '@ui'

// Custom styling with Mantine
import { Box, createStyles } from '@mantine/core'

const useStyles = createStyles((theme) => ({
  container: {
    padding: theme.spacing.md,
    backgroundColor: theme.colors.gray[0]
  }
}))
```

### 2. **CSS Modules (when needed)**
```css
/* components/CustomComponent/CustomComponent.module.css */
.container {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.header {
  font-weight: 600;
  color: var(--mantine-color-blue-6);
}
```

```typescript
// components/CustomComponent/CustomComponent.tsx
import styles from './CustomComponent.module.css'

export const CustomComponent = () => (
  <div className={styles.container}>
    <h2 className={styles.header}>Title</h2>
  </div>
)
```

## 🔧 Build and Deployment

### 1. **Development Build**
```bash
# Build all packages
pnpm build

# Build specific package
pnpm build:owner

# Clean and rebuild
pnpm clean && pnpm build
```

### 2. **Production Build**
```bash
# Production build with optimizations
NODE_ENV=production pnpm build

# Analyze bundle size
pnpm build:web --analyze
```

### 3. **Deployment Preparation**
```bash
# Check everything before deployment
pnpm deps:check
pnpm check-types
pnpm lint
pnpm test
pnpm build
```

## 🐛 Debugging

### 1. **Development Debugging**
```typescript
// Use React DevTools
// Enable in development
if (process.env.NODE_ENV === 'development') {
  import('@tanstack/react-query-devtools').then(({ ReactQueryDevtools }) => {
    // Query devtools available
  })
}
```

### 2. **Package Debugging**
```bash
# Debug specific package build
pnpm --filter @novaest/owner build --verbose

# Check package dependencies
pnpm --filter @novaest/owner list

# Analyze package bundle
cd packages/owner && pnpm build --analyze
```

### 3. **Common Issues**

**Build Errors:**
```bash
# Clear cache and rebuild
pnpm clean
rm -rf node_modules
pnpm install
pnpm build
```

**Type Errors:**
```bash
# Check TypeScript configuration
pnpm check-types --verbose

# Rebuild type definitions
pnpm build:packages
```

**Dependency Issues:**
```bash
# Check dependency health
pnpm deps:check

# Update dependencies
pnpm deps:update
```

## 📝 Code Standards

### 1. **TypeScript Guidelines**
- Use strict TypeScript configuration
- Define interfaces for all props and data structures
- Avoid `any` type - use proper typing
- Use Zod schemas for runtime validation

### 2. **React Guidelines**
- Use functional components with hooks
- Implement proper error boundaries
- Use React.memo for performance optimization
- Follow React best practices for state management

### 3. **Package Guidelines**
- Each package should have a single responsibility
- Export only necessary interfaces
- Document public APIs
- Follow semantic versioning

### 4. **Git Workflow**
```bash
# Feature development
git checkout -b feature/new-owner-dashboard
git add .
git commit -m "feat(owner): add project analytics dashboard"
git push origin feature/new-owner-dashboard
```

## 🚀 Performance Tips

### 1. **Development Performance**
- Use `pnpm --filter` for package-specific operations
- Leverage Turborepo caching
- Use React DevTools Profiler

### 2. **Build Performance**
- Utilize Turborepo's incremental builds
- Monitor bundle sizes
- Use code splitting effectively

### 3. **Runtime Performance**
- Implement lazy loading for role packages
- Use React.memo and useMemo appropriately
- Optimize re-renders with proper dependency arrays

This guide provides the foundation for effective development within the modular architecture. Follow these patterns for consistent, maintainable code across all packages.
